<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SystemAdminCompany;
use App\Models\SystemAdminModule;
use App\Models\SuperAdminModulePermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class SystemAdminStaffController extends Controller
{
    /**
     * Display staff management page
     */
    public function index()
    {
        // Only system admins can manage staff, not staff themselves
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied. Only system admins can manage staff.'));
        }

        // Get the system admin's company
        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        // Get all staff in the company (only staff type users)
        $staff = User::where('system_admin_company_id', $company->id)
                    ->where('type', 'staff')
                    ->with(['roles'])
                    ->paginate(10);

        return view('system_admin.staff.index', compact('staff', 'company'));
    }

    /**
     * Show form to create new staff
     */
    public function create()
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        // Get system admin specific permissions grouped by module
        $allPermissions = $this->getSystemAdminPermissions();

        return view('system_admin.staff.create', compact('company', 'allPermissions'));
    }

    /**
     * Store new staff member
     */
    public function store(Request $request)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'permissions' => 'array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Create staff user
        $staff = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'type' => 'staff', // Always create as staff type
            'system_admin_company_id' => $company->id,
            'lang' => 'en',
            'avatar' => '',
            'created_by' => auth()->id(),
            'email_verified_at' => now(),
        ]);

        // Assign selected permissions
        if ($request->has('permissions')) {
            $staff->givePermissionTo($request->permissions);
        }

        // Sync user to Automatish
        $this->syncUserToAutomatish($staff, $request->password);

        return redirect()->route('system-admin.staff.index')
            ->with('success', __('Staff member created successfully.'));
    }

    /**
     * Show form to edit staff
     */
    public function edit($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        $staff = User::where('id', $id)
                    ->where('system_admin_company_id', $company->id)
                    ->firstOrFail();

        // Get system admin specific permissions grouped by module
        $allPermissions = $this->getSystemAdminPermissions();

        // Get staff's current permissions
        $staffPermissions = $staff->permissions->pluck('name')->toArray();

        return view('system_admin.staff.edit', compact('staff', 'company', 'allPermissions', 'staffPermissions'));
    }

    /**
     * Update staff member
     */
    public function update(Request $request, $id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        $staff = User::where('id', $id)
                    ->where('system_admin_company_id', $company->id)
                    ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'password' => 'nullable|string|min:6',
            'permissions' => 'array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update staff details
        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'type' => 'staff', // Always keep as staff type
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $staff->update($updateData);

        // Update permissions
        $selectedPermissions = $request->input('permissions', []);
        $staff->syncPermissions($selectedPermissions);

        return redirect()->route('system-admin.staff.index')
            ->with('success', __('Staff member updated successfully.'));
    }

    /**
     * Delete staff member
     */
    public function destroy($id)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        $staff = User::where('id', $id)
                    ->where('system_admin_company_id', $company->id)
                    ->firstOrFail();

        // Don't allow deleting yourself
        if ($staff->id === auth()->id()) {
            return redirect()->back()->with('error', __('You cannot delete yourself.'));
        }

        // Delete the staff member
        $staff->delete();

        return redirect()->route('system-admin.staff.index')
            ->with('success', __('Staff member deleted successfully.'));
    }

    /**
     * Get system admin permissions grouped by module
     */
    private function getSystemAdminPermissions()
    {
        return [
            'Dashboard' => [
                'view system admin dashboard',
            ],
            'White Label' => [
                'view white label',
                'manage white label users',
                'create white label users',
                'edit white label users',
                'delete white label users',
            ],
            'Sub-accounts' => [
                'view sub accounts',
                'manage sub accounts',
                'create sub accounts',
                'edit sub accounts',
                'delete sub accounts',
            ],

            'Plan Management' => [
                'view plan management',
                'manage plans',
                'create plans',
                'edit plans',
                'delete plans',
            ],
            'Pricing Plans' => [
                'view pricing plans',
                'manage pricing plans',
                'create pricing plans',
                'edit pricing plans',
                'delete pricing plans',
            ],
            'Support System' => [
                'view support system',
                'manage support tickets',
                'create support tickets',
                'edit support tickets',
                'delete support tickets',
            ],

        ];
    }

    /**
     * Sync user to Automatish
     */
    private function syncUserToAutomatish($user, $plainPassword = null)
    {
        try {
            \Log::info('SystemAdminStaffController: Attempting to sync user to Automatish', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_type' => $user->type,
                'plain_password_provided' => $plainPassword ? 'Yes' : 'No'
            ]);

            $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);
            $result = $moduleIntegrationController->syncUserToAutomatish($user, $plainPassword);

            \Log::info('SystemAdminStaffController: Automatish sync result', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'sync_successful' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            \Log::error('SystemAdminStaffController: Failed to sync user to Automatish', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'error' => $e->getMessage(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
