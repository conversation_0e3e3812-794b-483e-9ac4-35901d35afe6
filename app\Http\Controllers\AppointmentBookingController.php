<?php

namespace App\Http\Controllers;

use App\Models\AppointmentBooking;
use App\Models\CalendarEvent;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AppointmentBookingController extends Controller
{
    /**
     * Display a listing of appointment bookings
     */
    public function index(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentBookingPermission('view appointment booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment booking viewing not available in your plan'
            ], 403);
        }

        try {
            $query = AppointmentBooking::with('event')
                ->whereHas('event', function($q) {
                    $q->where('created_by', Auth::id());
                });

            // Filter by specific event if provided
            if ($request->has('event_id')) {
                $query->where('event_id', $request->event_id);
            }

            // Filter by date range if provided
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('event_date', [$request->start_date, $request->end_date]);
            }

            $appointmentBookings = $query->orderBy('event_date', 'desc')
                                        ->orderBy('time_slots', 'desc')
                                        ->get();

            return response()->json([
                'success' => true,
                'data' => $appointmentBookings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch appointment bookings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new appointment booking
     */
    public function store(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentBookingPermission('create appointment booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment booking creation not available in your plan'
            ], 403);
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:calendar_events,id',
            'event_location' => 'required|string|max:255',
            'event_location_value' => 'nullable|string',
            'event_date' => 'required|date',
            'time_zone' => 'required|string|max:100',
            'time_slots' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get the calendar event
            $calendarEvent = CalendarEvent::findOrFail($request->event_id);

            // Check if the event belongs to the current user
            if ($calendarEvent->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to book this event'
                ], 403);
            }

            // Create the appointment booking
            $appointmentBooking = AppointmentBooking::create([
                'event_id' => $request->event_id,
                'event_location' => $request->event_location,
                'event_location_value' => $request->event_location_value,
                'event_date' => $request->event_date,
                'time_zone' => $request->time_zone,
                'time_slots' => $request->time_slots,
            ]);

            // Dispatch appointment scheduled webhook
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $appointmentData = [
                    'id' => $appointmentBooking->id,
                    'title' => $calendarEvent->title ?? 'Appointment Booking',
                    'event_date' => $request->event_date,
                    'time_slots' => $request->time_slots,
                    'time_zone' => $request->time_zone,
                    'location' => $request->event_location,
                    'location_value' => $request->event_location_value,
                    'event' => $calendarEvent->toArray(),
                    'status' => 'scheduled'
                ];
                $webhookDispatcher->dispatchAppointmentScheduled($appointmentData);
            } catch (\Exception $e) {
                Log::error('Appointment booking webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Appointment booking created successfully',
                'data' => $appointmentBooking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create appointment booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific appointment booking
     */
    public function show($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentBookingPermission('show appointment booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment booking viewing not available in your plan'
            ], 403);
        }

        try {
            $appointmentBooking = AppointmentBooking::with('event')->findOrFail($id);

            // Check if the appointment belongs to the current user's events
            if ($appointmentBooking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view this appointment booking'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => $appointmentBooking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch appointment booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an appointment booking
     */
    public function update(Request $request, $id)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentBookingPermission('edit appointment booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment booking editing not available in your plan'
            ], 403);
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'event_location' => 'sometimes|string|max:255',
            'event_location_value' => 'nullable|string',
            'event_date' => 'sometimes|date',
            'time_zone' => 'sometimes|string|max:100',
            'time_slots' => 'sometimes|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $appointmentBooking = AppointmentBooking::with('event')->findOrFail($id);

            // Check if the appointment belongs to the current user's events
            if ($appointmentBooking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to update this appointment booking'
                ], 403);
            }

            // Store old values for webhook comparison
            $oldEventDate = $appointmentBooking->event_date;
            $oldTimeSlots = $appointmentBooking->time_slots;
            $oldLocation = $appointmentBooking->event_location;
            $oldLocationValue = $appointmentBooking->event_location_value;

            // Update the appointment booking
            $appointmentBooking->update($request->only([
                'event_location',
                'event_location_value',
                'event_date',
                'time_zone',
                'time_slots'
            ]));

            // Dispatch appropriate webhooks based on what changed
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $appointmentData = [
                    'id' => $appointmentBooking->id,
                    'title' => $appointmentBooking->event->title ?? 'Appointment Booking',
                    'event_date' => $appointmentBooking->event_date,
                    'time_slots' => $appointmentBooking->time_slots,
                    'time_zone' => $appointmentBooking->time_zone,
                    'location' => $appointmentBooking->event_location,
                    'location_value' => $appointmentBooking->event_location_value,
                    'event' => $appointmentBooking->event->toArray()
                ];

                // Check if date or time changed (rescheduled)
                if ($request->has('event_date') && $oldEventDate != $appointmentBooking->event_date ||
                    $request->has('time_slots') && $oldTimeSlots != $appointmentBooking->time_slots) {

                    $oldDateTime = $oldEventDate . ' ' . $oldTimeSlots;
                    $newDateTime = $appointmentBooking->event_date . ' ' . $appointmentBooking->time_slots;
                    $webhookDispatcher->dispatchAppointmentRescheduled($appointmentData, $oldDateTime, $newDateTime);
                }

                // Check if location changed
                if ($request->has('event_location') && $oldLocation != $appointmentBooking->event_location ||
                    $request->has('event_location_value') && $oldLocationValue != $appointmentBooking->event_location_value) {

                    $oldLocationData = $oldLocation . ($oldLocationValue ? ': ' . $oldLocationValue : '');
                    $newLocationData = $appointmentBooking->event_location . ($appointmentBooking->event_location_value ? ': ' . $appointmentBooking->event_location_value : '');
                    $webhookDispatcher->dispatchAppointmentLocationChanged($appointmentData, $oldLocationData, $newLocationData);
                }

            } catch (\Exception $e) {
                Log::error('Appointment booking update webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Appointment booking updated successfully',
                'data' => $appointmentBooking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update appointment booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an appointment booking
     */
    public function destroy($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentBookingPermission('delete appointment booking')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment booking deletion not available in your plan'
            ], 403);
        }

        try {
            $appointmentBooking = AppointmentBooking::with('event')->findOrFail($id);

            // Check if the appointment belongs to the current user's events
            if ($appointmentBooking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to delete this appointment booking'
                ], 403);
            }

            // Dispatch appointment cancelled webhook before deletion
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $appointmentData = [
                    'id' => $appointmentBooking->id,
                    'title' => $appointmentBooking->event->title ?? 'Appointment Booking',
                    'event_date' => $appointmentBooking->event_date,
                    'time_slots' => $appointmentBooking->time_slots,
                    'time_zone' => $appointmentBooking->time_zone,
                    'location' => $appointmentBooking->event_location,
                    'location_value' => $appointmentBooking->event_location_value,
                    'event' => $appointmentBooking->event->toArray(),
                    'cancelled_at' => now()->toISOString()
                ];
                $webhookDispatcher->dispatchAppointmentCancelled($appointmentData, 'Appointment booking deleted');
            } catch (\Exception $e) {
                Log::error('Appointment booking cancellation webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            $appointmentBooking->delete();

            return response()->json([
                'success' => true,
                'message' => 'Appointment booking deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete appointment booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user has appointment booking permission based on pricing plan
     */
    private function hasAppointmentBookingPermission($permission)
    {
        $user = Auth::user();
        
        // Super admin and system admin always have access
        if ($user->type === 'super admin' || $user->type === 'system admin') {
            return true;
        }

        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            return $user->hasModulePermission('booking', $permission);
        }

        // For other user types, check traditional permissions
        return $user->can($permission);
    }
}
