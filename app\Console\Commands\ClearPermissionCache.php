<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class ClearPermissionCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:clear-cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear permission cache to fix module visibility issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Clearing permission cache...');
        
        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        
        // Clear general cache
        Artisan::call('cache:clear');
        
        // Clear config cache
        Artisan::call('config:clear');
        
        // Clear view cache
        Artisan::call('view:clear');
        
        $this->info('Permission cache cleared successfully!');
        $this->info('All caches have been cleared.');
        
        return Command::SUCCESS;
    }
}
