<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\LeadStage;
use App\Models\Pipeline;
use App\Models\LeadActivityLog;
use App\Models\Utility;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;

class LeadStageController extends Controller
{

    public function __construct()
    {
        $this->middleware(
            [
                'auth',
                'XSS',
            ]
        );
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(\Auth::user()->can('manage lead stage'))
        {
            $lead_stages = LeadStage::select('lead_stages.*', 'pipelines.name as pipeline')->join('pipelines', 'pipelines.id', '=', 'lead_stages.pipeline_id')->where('pipelines.created_by', '=', \Auth::user()->ownerId())->where('lead_stages.created_by', '=', \Auth::user()->ownerId())->orderBy('lead_stages.pipeline_id')->orderBy('lead_stages.order')->get();
            $pipelines   = [];

            foreach($lead_stages as $lead_stage)
            {
                if(!array_key_exists($lead_stage->pipeline_id, $pipelines))
                {
                    $pipelines[$lead_stage->pipeline_id]                = [];
                    $pipelines[$lead_stage->pipeline_id]['name']        = $lead_stage['pipeline'];
                    $pipelines[$lead_stage->pipeline_id]['lead_stages'] = [];
                }
                $pipelines[$lead_stage->pipeline_id]['lead_stages'][] = $lead_stage;
            }

            return view('lead_stages.index')->with('pipelines', $pipelines);
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if(\Auth::user()->can('create lead stage'))
        {
            $pipelines = Pipeline::where('created_by', '=', \Auth::user()->ownerId())->get()->pluck('name', 'id');

            return view('lead_stages.create')->with('pipelines', $pipelines);
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if(\Auth::user()->can('create lead stage'))
        {
            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required|max:20',
                                   'pipeline_id' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->route('lead_stages.index')->with('error', $messages->first());
            }
            $lead_stage              = new LeadStage();
            $lead_stage->name        = $request->name;
            $lead_stage->pipeline_id = $request->pipeline_id;
            $lead_stage->created_by  = \Auth::user()->ownerId();
            $lead_stage->save();

            return redirect()->route('lead_stages.index')->with('success', __('Lead Stage successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param \App\LeadStage $leadStage
     *
     * @return \Illuminate\Http\Response
     */
    public function show(LeadStage $leadStage)
    {
        return redirect()->route('lead_stages.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\LeadStage $leadStage
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(LeadStage $leadStage)
    {
        if(\Auth::user()->can('edit lead stage'))
        {
            if($leadStage->created_by == \Auth::user()->ownerId())
            {
                $pipelines = Pipeline::where('created_by', '=', \Auth::user()->ownerId())->get()->pluck('name', 'id');

                return view('lead_stages.edit', compact('leadStage', 'pipelines'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\LeadStage $leadStage
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, LeadStage $leadStage)
    {
        if(\Auth::user()->can('edit lead stage'))
        {

            if($leadStage->created_by == \Auth::user()->ownerId())
            {

                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required|max:20',
                                       'pipeline_id' => 'required',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('lead_stages.index')->with('error', $messages->first());
                }

                $leadStage->name        = $request->name;
                $leadStage->pipeline_id = $request->pipeline_id;
                $leadStage->save();

                return redirect()->route('lead_stages.index')->with('success', __('Lead Stage successfully updated!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\LeadStage $leadStage
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(LeadStage $leadStage)
    {
        if(\Auth::user()->can('delete lead stage'))
        {
            $leadStage->delete();

            return redirect()->route('lead_stages.index')->with('success', __('Lead Stage successfully deleted!'));

        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    public function order(Request $request)
    {
        $post = $request->all();
        foreach($post['order'] as $key => $item)
        {
            $lead_stage        = LeadStage::where('id', '=', $item)->first();
            $lead_stage->order = $key;
            $lead_stage->save();
        }

        return response()->json(['success' => __('Lead stages sorted successfully!')]);
    }

    public function updateStage(Request $request, $leadId)
    {
        if(\Auth::user()->can('move lead'))
        {
            $request->validate([
                'stage_id' => 'required|integer|exists:lead_stages,id',
            ]);

            $lead = Lead::findOrFail($leadId);
            $oldStage = $lead->stage;
            $newStage = LeadStage::find($request->stage_id);

            // Update the lead's stage
            $lead->stage_id = $request->stage_id;
            $lead->save();

            // Log the activity
            LeadActivityLog::create([
                'user_id' => \Auth::user()->id,
                'lead_id' => $lead->id,
                'log_type' => 'Move',
                'remark' => json_encode([
                    'title' => $lead->name,
                    'old_status' => $oldStage->name,
                    'new_status' => $newStage->name,
                ]),
            ]);

            // Prepare email data
            $lead_users = $lead->users->pluck('email', 'id')->toArray();
            $lArr = [
                'lead_name' => $lead->name,
                'lead_email' => $lead->email,
                'lead_pipeline' => $lead->pipeline->name,
                'lead_stage' => $oldStage->name,
                'lead_old_stage' => $oldStage->name,
                'lead_new_stage' => $newStage->name,
            ];

            // Send Email notification
            Utility::sendEmailTemplate('Move Lead', $lead_users, $lArr);

            // Send webhook for stage change
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStage->id, $request->stage_id);
            } catch (\Exception $e) {
                \Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Lead moved from ' . $oldStage->name . ' to ' . $newStage->name,
                'lead_id' => $leadId,
                'old_stage_id' => $oldStage->id,
                'new_stage_id' => $lead->stage_id,
            ]);
        }
        else
        {
            return response()->json([
                'status' => 'error',
                'message' => __('Permission Denied.')
            ], 401);
        }
    }

    public function moveLeadToStage(Request $request)
    {
        if(\Auth::user()->can('move lead'))
        {
            $request->validate([
                'lead_id' => 'required|integer|exists:leads,id',
                'stage_id' => 'required|integer|exists:lead_stages,id',
                'order' => 'array'
            ]);

            $lead = Lead::findOrFail($request->lead_id);
            $oldStage = $lead->stage;
            $newStage = LeadStage::find($request->stage_id);

            // Only log and send notifications if stage actually changed
            if($lead->stage_id != $request->stage_id)
            {
                // Log the activity
                LeadActivityLog::create([
                    'user_id' => \Auth::user()->id,
                    'lead_id' => $lead->id,
                    'log_type' => 'Move',
                    'remark' => json_encode([
                        'title' => $lead->name,
                        'old_status' => $oldStage->name,
                        'new_status' => $newStage->name,
                    ]),
                ]);

                // Prepare email data
                $lead_users = $lead->users->pluck('email', 'id')->toArray();
                $lArr = [
                    'lead_name' => $lead->name,
                    'lead_email' => $lead->email,
                    'lead_pipeline' => $lead->pipeline->name,
                    'lead_stage' => $oldStage->name,
                    'lead_old_stage' => $oldStage->name,
                    'lead_new_stage' => $newStage->name,
                ];

                // Send Email notification
                Utility::sendEmailTemplate('Move Lead', $lead_users, $lArr);

                // Send webhook for stage change
                try {
                    $webhookDispatcher = new CrmWebhookDispatcher();
                    $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStage->id, $request->stage_id);
                } catch (\Exception $e) {
                    \Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
                }
            }

            // Update lead stage
            $lead->stage_id = $request->stage_id;
            $lead->save();

            // Update order for all leads in the new stage if order array is provided
            if($request->has('order') && is_array($request->order))
            {
                foreach($request->order as $key => $leadId)
                {
                    $leadToUpdate = Lead::find($leadId);
                    if($leadToUpdate)
                    {
                        $leadToUpdate->order = $key;
                        $leadToUpdate->save();
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => __('Lead successfully moved.'),
                'lead_id' => $lead->id,
                'old_stage_id' => $oldStage->id,
                'new_stage_id' => $lead->stage_id,
            ]);
        }
        else
        {
            return response()->json([
                'status' => 'error',
                'message' => __('Permission Denied.')
            ], 401);
        }
    }
}
