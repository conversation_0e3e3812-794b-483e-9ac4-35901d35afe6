<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CrmWebhookDispatcher;
use App\Models\Lead;

class TestAllWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:test-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all webhook events';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing all webhook events...');
        
        // Create sample lead data
        $sampleLead = (object) [
            'id' => 999,
            'name' => 'Test Lead',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'pipeline_id' => 1,
            'stage_id' => 1,
            'user_id' => 1,
            'created_at' => now(),
        ];
        
        $webhookDispatcher = new CrmWebhookDispatcher();
        
        $tests = [
            'Lead Created' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadCreated($sampleLead);
            },
            'Lead Updated' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadUpdated($sampleLead, ['name' => 'Updated Name']);
            },
            'Lead Deleted' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadDeleted($sampleLead);
            },
            'Lead Stage Changed' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadStageChanged($sampleLead, 1, 2);
            },
            'Lead Assigned' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatch('crm.lead_assigned', $sampleLead, ['assigned_user_id' => 2]);
            },
            'Lead Unassigned' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatch('crm.lead_unassigned', $sampleLead, ['previous_user_id' => 1]);
            },
        ];
        
        foreach ($tests as $testName => $testFunction) {
            $this->info("\nTesting: {$testName}");
            
            try {
                $result = $testFunction();
                
                $successCount = 0;
                $totalCount = count($result);
                
                foreach ($result as $moduleName => $moduleResult) {
                    $status = $moduleResult['success'] ?? false ? 'SUCCESS' : 'FAILED';
                    $this->line("  - {$moduleName}: {$status}");
                    
                    if ($moduleResult['success'] ?? false) {
                        $successCount++;
                    } else {
                        $error = $moduleResult['error'] ?? 'Unknown error';
                        $this->error("    Error: {$error}");
                    }
                }
                
                $this->info("  Result: {$successCount}/{$totalCount} modules successful");
                
            } catch (\Exception $e) {
                $this->error("  Failed: " . $e->getMessage());
            }
        }
        
        return 0;
    }
}
