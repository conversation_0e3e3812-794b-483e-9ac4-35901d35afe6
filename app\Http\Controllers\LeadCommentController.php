<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Models\LeadComment;
use App\Models\LeadCommentReaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LeadCommentController extends Controller
{
    public function store(Request $request, $leadId)
{
    // Add debugging
    \Log::info('Comment store request:', $request->all());
    
    $request->validate([
        'comment' => 'required|string|max:1000',
        'parent_id' => 'nullable|exists:lead_comments,id',
        'comment_files.*' => 'nullable|file|max:10240'
    ]);

    $lead = Lead::findOrFail($leadId);
    
    // Create comment
    $comment = new LeadComment();
    $comment->lead_id = $lead->id;
    $comment->user_id = Auth::id();
    $comment->comment = $request->comment;
    $comment->parent_id = $request->parent_id; // This should save the parent_id
    $comment->created_by = Auth::user()->creatorId();
    
    // Add debugging
    \Log::info('Saving comment with parent_id:', ['parent_id' => $request->parent_id]);
    
    $comment->save();

    return redirect()->back()->with('success', __('Comment added successfully.'));
}
    public function destroy($commentId)
    {
        $comment = LeadComment::findOrFail($commentId);
        
        // Check if user can delete this comment
        if ($comment->user_id != Auth::id() && Auth::user()->type != 'super admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }
        
        $comment->delete();
        
        return response()->json(['success' => true, 'message' => 'Comment deleted successfully']);
    }

    public function react(Request $request, $commentId)
{
    $request->validate([
        'reaction' => 'required|in:like,love,laugh,angry,sad,wow'
    ]);

    $comment = LeadComment::findOrFail($commentId);
    $username = Auth::user()->name;
    
    // Get existing reactions
    $reactions = $comment->comment_reaction ?? [];
    
    // Remove any existing reaction from this user (by username)
    $reactions = array_filter($reactions, function($item) use ($username) {
        return $item['username'] !== $username;
    });
    
    // Add new reaction
    $reactions[] = [
        'reaction' => $request->reaction,
        'username' => $username
    ];
    
    $comment->comment_reaction = $reactions;
    $comment->save();

    return response()->json([
        'success' => true,
        'reactions' => $this->getReactionCounts($reactions),
        'userReaction' => $request->reaction
    ]);
}

public function unreact($commentId)
{
    $comment = LeadComment::findOrFail($commentId);
    $username = Auth::user()->name;
    
    // Get existing reactions and remove this user's reaction
    $reactions = $comment->comment_reaction ?? [];
    $reactions = array_filter($reactions, function($item) use ($username) {
        return $item['username'] !== $username;
    });
    
    // Re-index array
    $reactions = array_values($reactions);
    
    $comment->comment_reaction = $reactions;
    $comment->save();

    return response()->json([
        'success' => true,
        'reactions' => $this->getReactionCounts($reactions),
        'userReaction' => null
    ]);
}

// Helper method to count reactions
private function getReactionCounts($reactions)
{
    $counts = [];
    foreach ($reactions as $item) {
        $reaction = $item['reaction'];
        if (!isset($counts[$reaction])) {
            $counts[$reaction] = 0;
        }
        $counts[$reaction]++;
    }
    return $counts;
}
}