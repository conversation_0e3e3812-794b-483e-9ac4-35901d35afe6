<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Constants\CrmWebhookActions;
use App\Services\CrmWebhookDispatcher;

class TestDealWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deals:test-webhooks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all deal webhook actions to verify they are properly defined';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing Deal Webhook Actions...');
        
        // Get all deal-related actions
        $dealActions = [
            'DEAL_CREATED' => CrmWebhookActions::DEAL_CREATED,
            'DEAL_UPDATED' => CrmWebhookActions::DEAL_UPDATED,
            'DEAL_DELETED' => CrmWebhookActions::DEAL_DELETED,
            'DEAL_STAGE_CHANGED' => CrmWebhookActions::DEAL_STAGE_CHANGED,
            'DEAL_STATUS_CHANGED' => CrmWebhookActions::DEAL_STATUS_CHANGED,
            'DEAL_ASSIGNED' => CrmWebhookActions::DEAL_ASSIGNED,
            'DEAL_UNASSIGNED' => CrmWebhookActions::DEAL_UNASSIGNED,
            'DEAL_WON' => CrmWebhookActions::DEAL_WON,
            'DEAL_LOST' => CrmWebhookActions::DEAL_LOST,
            'DEAL_DEADLINE_APPROACHING' => CrmWebhookActions::DEAL_DEADLINE_APPROACHING,
            'DEAL_INACTIVE_FOR_DAYS' => CrmWebhookActions::DEAL_INACTIVE_FOR_DAYS,
            'DEAL_NOTES_ADDED' => CrmWebhookActions::DEAL_NOTES_ADDED,
            'DEAL_DOCUMENTS_UPLOADED' => CrmWebhookActions::DEAL_DOCUMENTS_UPLOADED,
        ];

        $this->info("\n--- Deal Webhook Actions ---");
        foreach ($dealActions as $name => $action) {
            $this->line("✓ {$name}: {$action}");
        }

        // Test dispatcher methods
        $this->info("\n--- Testing Dispatcher Methods ---");
        $dispatcher = new CrmWebhookDispatcher();
        $dispatcherMethods = [
            'dispatchDealCreated',
            'dispatchDealUpdated',
            'dispatchDealDeleted',
            'dispatchDealStageChanged',
            'dispatchDealStatusChanged',
            'dispatchDealAssigned',
            'dispatchDealUnassigned',
            'dispatchDealWon',
            'dispatchDealLost',
            'dispatchDealDeadlineApproaching',
            'dispatchDealInactiveForDays',
            'dispatchDealNotesAdded',
            'dispatchDealDocumentsUploaded',
        ];

        foreach ($dispatcherMethods as $method) {
            if (method_exists($dispatcher, $method)) {
                $this->info("✓ {$method}");
            } else {
                $this->error("✗ {$method} - Method not found!");
            }
        }

        // Test action validation
        $this->info("\n--- Testing Action Validation ---");
        foreach ($dealActions as $name => $action) {
            if (CrmWebhookActions::isValidAction($action)) {
                $this->info("✓ {$action} - Valid");
            } else {
                $this->error("✗ {$action} - Invalid!");
            }
        }

        // Integration status
        $this->info("\n--- Integration Status ---");
        $integrations = [
            'Deal Created' => 'DealController@store',
            'Deal Updated' => 'DealController@update',
            'Deal Deleted' => 'DealController@destroy',
            'Deal Stage Changed' => 'DealController@order',
            'Deal Status Changed' => 'DealController@changeStatus',
            'Deal Assigned' => 'DealController@userUpdate',
            'Deal Unassigned' => 'DealController@userDestroy',
            'Deal Notes Added' => 'DealController@noteStore',
            'Deal Documents Uploaded' => 'DealController@fileUpload',
            'Deal Deadline Approaching' => 'Command/Service (deals:check-webhooks)',
            'Deal Inactive for Days' => 'Command/Service (deals:check-webhooks)',
        ];

        foreach ($integrations as $webhook => $location) {
            $this->line("✓ {$webhook} → {$location}");
        }

        $this->info("\n--- Summary ---");
        $this->info("Total Deal Actions: " . count($dealActions));
        $this->info("Total Dispatcher Methods: " . count($dispatcherMethods));
        $this->info("All webhook actions are properly defined and integrated!");

        $this->warn("\nNote: This test only verifies that actions and methods exist.");
        $this->warn("To test actual webhook delivery, trigger the actions in your application.");

        return Command::SUCCESS;
    }
}
