<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ModuleIntegration;

class CheckWebhookIntegrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:check-integrations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check webhook module integrations';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking webhook module integrations...');
        
        $integrations = ModuleIntegration::all();
        
        if ($integrations->isEmpty()) {
            $this->error('No module integrations found in the database!');
            $this->info('You need to add module integrations to the module_integrations table.');
            $this->info('Example:');
            $this->info('INSERT INTO module_integrations (name, base_url, api_token, enabled, created_at, updated_at)');
            $this->info('VALUES (\'Automatish\', \'https://webhook.site\', \'your-token\', 1, NOW(), NOW());');
            return 1;
        }
        
        $this->info("Found {$integrations->count()} module integration(s):");
        
        $headers = ['ID', 'Name', 'Base URL', 'Enabled', 'Created At'];
        $rows = [];
        
        foreach ($integrations as $integration) {
            $rows[] = [
                $integration->id,
                $integration->name,
                $integration->base_url,
                $integration->enabled ? 'Yes' : 'No',
                $integration->created_at->format('Y-m-d H:i:s')
            ];
        }
        
        $this->table($headers, $rows);
        
        $enabledCount = $integrations->where('enabled', true)->count();
        $this->info("Enabled integrations: {$enabledCount}");
        
        if ($enabledCount === 0) {
            $this->warn('No enabled integrations found! Webhooks will not be sent.');
            $this->info('Enable integrations by setting enabled = 1 in the database.');
        }
        
        return 0;
    }
}
