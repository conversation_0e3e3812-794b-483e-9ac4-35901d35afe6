<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectFile;
use App\Models\ProjectFolder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProjectFileController extends Controller
{
    /**
     * Get files and folders for a project
     */
    public function index(Project $project, Request $request)
    {
        $folderId = $request->get('folder_id');
        
        // Get folders in current directory with file counts
        $folders = ProjectFolder::where('project_id', $project->id)
            ->where('parent_folder_id', $folderId)
            ->with(['files', 'childFolders.files'])
            ->orderBy('name')
            ->get();
        
        // Get files in current directory
        $files = ProjectFile::where('project_id', $project->id)
            ->where('folder_id', $folderId)
            ->orderBy('name')
            ->get();
        
        // Get current folder for breadcrumb
        $currentFolder = $folderId ? ProjectFolder::find($folderId) : null;
        
        // Ensure folder counts are calculated
        $folders->each(function ($folder) {
            // Force calculation of total_files_count
            $folder->total_files_count;
        });

        return response()->json([
            'folders' => $folders,
            'files' => $files,
            'currentFolder' => $currentFolder,
            'breadcrumb' => $currentFolder ? $currentFolder->path : []
        ]);
    }

    /**
     * Create a new folder
     */
    public function createFolder(Project $project, Request $request)
    {
        // Debug log
        \Log::info('Creating folder', [
            'project_id' => $project->id,
            'request_data' => $request->all()
        ]);

        $validationRules = [
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:50',
            'color' => 'nullable|string|max:7',
            'description' => 'nullable|string'
        ];

        // Only validate parent_folder_id if it's not null and not empty string
        if ($request->parent_folder_id !== null && $request->parent_folder_id !== '') {
            $validationRules['parent_folder_id'] = 'exists:project_folders,id';
        }

        $request->validate($validationRules);

        // Convert empty string to null for parent_folder_id
        $parentFolderId = $request->parent_folder_id ?: null;

        // Additional validation: if parent_folder_id is provided, ensure it belongs to the same project
        if ($parentFolderId) {
            $parentFolder = ProjectFolder::find($parentFolderId);
            if (!$parentFolder || $parentFolder->project_id != $project->id) {
                return response()->json([
                    'success' => false,
                    'message' => __('Invalid parent folder')
                ], 422);
            }
        }

        $folder = ProjectFolder::create([
            'name' => $request->name,
            'project_id' => $project->id,
            'parent_folder_id' => $parentFolderId,
            'icon' => $request->icon ?? 'ti-folder-filled',
            'color' => $request->color ?? '#3b82f6',
            'description' => $request->description,
            'created_by' => Auth::id()
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Folder created successfully'),
            'folder' => $folder
        ]);
    }

    /**
     * Upload files
     */
    public function uploadFiles(Project $project, Request $request)
    {
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'file|max:10240', // 10MB max per file
            'folder_id' => 'nullable|exists:project_folders,id'
        ]);

        $uploadedFiles = [];

        foreach ($request->file('files') as $file) {
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = Str::uuid() . '.' . $extension;
            
            $filePath = $file->storeAs('project_files/' . $project->id, $fileName, 'public');

            $projectFile = ProjectFile::create([
                'name' => pathinfo($originalName, PATHINFO_FILENAME),
                'original_name' => $originalName,
                'file_path' => $filePath,
                'file_type' => $extension,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'project_id' => $project->id,
                'folder_id' => $request->folder_id,
                'uploaded_by' => Auth::id()
            ]);

            $uploadedFiles[] = $projectFile;
        }

        return response()->json([
            'success' => true,
            'message' => __('Files uploaded successfully'),
            'files' => $uploadedFiles
        ]);
    }

    /**
     * Download a file
     */
    public function download(Project $project, ProjectFile $file)
    {
        if (!$file->canAccess(Auth::user())) {
            abort(403);
        }

        if (!Storage::disk('public')->exists($file->file_path)) {
            abort(404);
        }

        return Storage::disk('public')->download($file->file_path, $file->original_name);
    }

    /**
     * Delete a file
     */
    public function deleteFile(Project $project, ProjectFile $file)
    {
        if (!$file->canAccess(Auth::user())) {
            abort(403);
        }

        $file->delete();

        return response()->json([
            'success' => true,
            'message' => __('File deleted successfully')
        ]);
    }

    /**
     * Delete a folder
     */
    public function deleteFolder(Project $project, ProjectFolder $folder)
    {
        if (!$folder->canAccess(Auth::user())) {
            abort(403);
        }

        // Delete all files and subfolders recursively
        $this->deleteFolderRecursively($folder);

        return response()->json([
            'success' => true,
            'message' => __('Folder deleted successfully')
        ]);
    }

    /**
     * Recursively delete folder and its contents
     */
    private function deleteFolderRecursively(ProjectFolder $folder)
    {
        // Delete all files in this folder
        foreach ($folder->files as $file) {
            $file->delete();
        }

        // Delete all subfolders recursively
        foreach ($folder->childFolders as $childFolder) {
            $this->deleteFolderRecursively($childFolder);
        }

        // Delete the folder itself
        $folder->delete();
    }

    /**
     * Rename a file
     */
    public function renameFile(Project $project, ProjectFile $file, Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        if (!$file->canAccess(Auth::user())) {
            abort(403);
        }

        $file->update(['name' => $request->name]);

        return response()->json([
            'success' => true,
            'message' => __('File renamed successfully'),
            'file' => $file
        ]);
    }

    /**
     * Rename a folder
     */
    public function renameFolder(Project $project, ProjectFolder $folder, Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        if (!$folder->canAccess(Auth::user())) {
            abort(403);
        }

        $folder->update(['name' => $request->name]);

        return response()->json([
            'success' => true,
            'message' => __('Folder renamed successfully'),
            'folder' => $folder
        ]);
    }
}
