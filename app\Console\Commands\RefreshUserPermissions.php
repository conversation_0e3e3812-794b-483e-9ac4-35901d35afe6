<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Permission;

class RefreshUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:refresh-permissions {--user-id= : Specific user ID to refresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh user permissions based on their module permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        if ($userId) {
            $users = User::where('id', $userId)->get();
            if ($users->isEmpty()) {
                $this->error("User with ID {$userId} not found.");
                return 1;
            }
        } else {
            $users = User::whereIn('type', ['company', 'employee'])->get();
        }

        $this->info('Refreshing user permissions...');
        
        foreach ($users as $user) {
            $this->info("Processing user: {$user->name} (ID: {$user->id}, Type: {$user->type})");
            
            try {
                if ($user->type === 'company' && $user->pricingPlan) {
                    // For company users, use pricing plan permissions
                    $user->assignPricingPlanPermissions();
                    $this->info("  ✓ Assigned pricing plan permissions");
                } elseif ($user->type === 'employee' && !empty($user->module_permissions)) {
                    // For employees, use their module permissions
                    $user->assignEmployeeModulePermissions();
                    $this->info("  ✓ Assigned employee module permissions");
                }
                
                // Refresh permissions
                $user->refreshPermissions();
                $this->info("  ✓ Refreshed permissions cache");
                
            } catch (\Exception $e) {
                $this->error("  ✗ Error processing user {$user->id}: " . $e->getMessage());
            }
        }
        
        // Clear global permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $this->info('✓ Cleared global permission cache');
        
        $this->info('User permissions refresh completed!');
        return 0;
    }
}
