<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SystemAdminStaffMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Allow system admins full access
        if ($user->type === 'system admin') {
            return $next($request);
        }

        // Check if user is staff
        if ($user->type !== 'staff') {
            return redirect()->back()->with('error', __('Access denied. Staff access required.'));
        }

        // Check if staff has company association
        if (!$user->system_admin_company_id) {
            return redirect()->back()->with('error', __('Access denied. No company association found.'));
        }

        // If specific permission is required, check it
        if ($permission && !$user->can($permission)) {
            return redirect()->back()->with('error', __('Access denied. Insufficient permissions.'));
        }

        return $next($request);
    }
}
