<?php

namespace App\Http\Controllers;

use App\Models\CustomField;
use App\Models\Employee;
use App\Models\ExperienceCertificate;
use App\Models\GenerateOfferLetter;
use App\Models\JoiningLetter;
use App\Models\LoginDetail;
use App\Models\NOC;
use App\Models\Order;
use App\Models\Plan;
use App\Models\User;
use App\Models\UserToDo;
use App\Models\Utility;
use App\Models\ModuleIntegration;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Lab404\Impersonate\Impersonate;
use Spatie\Permission\Models\Role;
use App\Models\ReferralTransaction;
use App\Models\ReferralSetting;
use App\Models\SystemAdminModule;
use App\Models\SuperAdminModulePermission;
use Illuminate\Validation\Rule;

class UserController extends Controller
{

    public function index()
    {
        User::defaultEmail();

        $user = \Auth::user();

        // Check permissions for different user types
        $hasAccess = false;
        if (\Auth::user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (\Auth::user()->type == 'staff') {
            $hasAccess = \Auth::user()->can('view white label');
        } else {
            $hasAccess = \Auth::user()->can('manage user');
        }

        if ($hasAccess) {
            if (\Auth::user()->type == 'system admin') {
                // System admin can only see super admin users
                $users = User::where('type', '=', 'super admin')->with(['currentPlan'])->get();
            } elseif (\Auth::user()->type == 'staff') {
                // Staff can see super admin users from their company
                $company = \Auth::user()->systemAdminCompany;
                if ($company) {
                    $users = User::where('type', '=', 'super admin')
                                ->where('system_admin_company_id', $company->id)
                                ->with(['currentPlan'])->get();
                } else {
                    $users = collect(); // Empty collection if no company
                }
            } elseif (\Auth::user()->type == 'super admin') {
                $users = User::where('created_by', '=', $user->creatorId())->where('type', '=', 'company')->with(['currentPlan'])->get();
            } else {
                $users = User::where('created_by', '=', $user->creatorId())->where('type', '!=', 'client')->with(['currentPlan'])->get();
            }

            return view('user.index')->with('users', $users);
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    public function create()
    {
        // Check permissions for different user types
        $hasAccess = false;
        if (\Auth::user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (\Auth::user()->type == 'staff') {
            $hasAccess = \Auth::user()->can('create white label users');
        } else {
            $hasAccess = \Auth::user()->can('create user');
        }

        if (!$hasAccess) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'user')->get();
        $user = \Auth::user();

        // System admin and staff can only create super admin roles
        if ($user->type == 'system admin' || $user->type == 'staff') {
            $roles = Role::where('name', 'super admin')->get()->pluck('name', 'id');
            // Get available modules for super admin creation
            $availableModules = [
                'whatsapp_flows' => 'WhatsApp Flows',
                'whatsapp_orders' => 'WhatsApp Orders',
                'campaigns' => 'Campaigns',
                'templates' => 'Templates',
                'chatbot' => 'Chatbot'
            ];
        } elseif ($user->type == 'super admin') {
            $roles = Role::where('created_by', '=', $user->creatorId())->where('name', '!=', 'client')->get()->pluck('name', 'id');

        // Get module permissions JSON from super admin user record
        $modulePermissionsJson = $user->module_permissions ?? '[]';
        $modulePermissions = json_decode($modulePermissionsJson, true);

        // For company users, get module permissions from PricingPlan
        if ($user->type === 'company') {
            $pricingPlan = \App\Models\PricingPlan::find($user->plan);
            if ($pricingPlan) {
                $modulePermissions = $pricingPlan->module_permissions ?? [];
            } else {
                $modulePermissions = [];
            }
        }

            // Full list of modules
            $allModules = [
                'whatsapp_flows' => 'WhatsApp Flows',
                'whatsapp_orders' => 'WhatsApp Orders',
                'campaigns' => 'Campaigns',
                'templates' => 'Templates',
                'chatbot' => 'Chatbot'
            ];

            // Filter modules to only those in modulePermissions
            $availableModules = array_filter($allModules, function ($key) use ($modulePermissions) {
                return in_array($key, $modulePermissions);
            }, ARRAY_FILTER_USE_KEY);

        } else {
            $roles = Role::where('created_by', '=', $user->creatorId())->where('name', '!=', 'client')->get()->pluck('name', 'id');
            $availableModules = [];
            $modulePermissions = [];
        }

        // Check if OMX Flow is already enabled through pricing plan
        $omxFlowEnabledViaPlan = isset($modulePermissions['omx_flow']) && !empty($modulePermissions['omx_flow']);

        if (\Auth::user()->can('create user')) {
            return view('user.create', compact('roles', 'customFields', 'availableModules', 'omxFlowEnabledViaPlan'));
        } else {
            return redirect()->back();
        }
    }

    /**
     * Sync user to all enabled modules
     */
    private function syncUserToModules(User $user, $plainPassword = null)
    {
        try {
            $modules = ModuleIntegration::enabled()->get();

            Log::info('Attempting to sync user to modules', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_type' => $user->type,
                'enabled_modules_count' => $modules->count(),
                'modules' => $modules->pluck('name')->toArray()
            ]);

            if ($modules->count() === 0) {
                Log::warning('No enabled modules found for user sync', [
                    'user_id' => $user->id,
                    'total_modules' => ModuleIntegration::count()
                ]);
                return;
            }

            $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);

            foreach ($modules as $module) {
                Log::info('Syncing user to module', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'user_name' => $user->name,
                    'module_name' => $module->name,
                    'module_enabled' => $module->enabled,
                    'sync_endpoint' => $module->sync_endpoint,
                    'base_url' => $module->base_url,
                    'plain_password_provided' => $plainPassword ? 'Yes' : 'No'
                ]);

                // Handle Automatish specifically with external-signup endpoint
                if (strtolower($module->name) === 'automatish') {
                    Log::info('Using Automatish-specific sync method', [
                        'user_id' => $user->id,
                        'module_name' => $module->name,
                        'plain_password_length' => $plainPassword ? strlen($plainPassword) : 0
                    ]);
                    $result = $moduleIntegrationController->syncUserToAutomatish($user, $plainPassword);
                } else {
                    Log::info('Using general module sync method', [
                        'user_id' => $user->id,
                        'module_name' => $module->name
                    ]);
                    $result = $moduleIntegrationController->syncUserToModule($user, $module);
                }

                Log::info('Module sync result', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'module_name' => $module->name,
                    'sync_successful' => $result,
                    'sync_method' => strtolower($module->name) === 'automatish' ? 'Automatish-specific' : 'General'
                ]);
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the user creation
            Log::error('User sync to modules failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync user updates to all enabled modules
     */
    private function syncUserUpdateToModules(User $user, array $updatedFields = [])
    {
        try {
            $modules = ModuleIntegration::enabled()->get();
            
            Log::info('Attempting to sync user update to modules', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_type' => $user->type,
                'updated_fields' => $updatedFields,
                'enabled_modules_count' => $modules->count(),
                'modules' => $modules->pluck('name')->toArray()
            ]);
            
            if ($modules->count() === 0) {
                Log::warning('No enabled modules found for user update sync', [
                    'user_id' => $user->id,
                    'total_modules' => ModuleIntegration::count()
                ]);
                return;
            }
            
            foreach ($modules as $module) {
                if (!$module->enabled || !$module->sync_endpoint) {
                    continue;
                }

                try {
                    $updateUrl = rtrim($module->base_url, '/') . '/' . ltrim($module->sync_endpoint, '/');
                    
                    // Prepare update data
                    $updateData = [
                        'email' => $user->email,
                    ];
                    
                    // Add updated fields
                    if (in_array('name', $updatedFields)) {
                        $updateData['name'] = $user->name;
                    }
                    if (in_array('type', $updatedFields)) {
                        $updateData['type'] = $user->type;
                    }

                    $response = \Illuminate\Support\Facades\Http::withHeaders([
                        'Authorization' => 'Bearer ' . $module->api_token,
                        'Content-Type' => 'application/json',
                    ])->put($updateUrl, $updateData);

                    Log::info('User update sync to module', [
                        'user_id' => $user->id,
                        'module_name' => $module->name,
                        'update_data' => $updateData,
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);

                } catch (\Exception $e) {
                    Log::error('User update sync to module failed', [
                        'user_id' => $user->id,
                        'module_name' => $module->name,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the user update
            Log::error('User update sync to modules failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync user deletion to all enabled modules
     */
    private function syncUserDeleteToModules(User $user, $forceDelete = false)
    {
        $results = [];
        $hasErrors = false;
        
        try {
            $modules = ModuleIntegration::enabled()->get();
            
            Log::info('Attempting to sync user deletion to modules', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_type' => $user->type,
                'force_delete' => $forceDelete,
                'enabled_modules_count' => $modules->count(),
                'modules' => $modules->pluck('name')->toArray()
            ]);
            
            if ($modules->count() === 0) {
                Log::warning('No enabled modules found for user deletion sync', [
                    'user_id' => $user->id,
                    'total_modules' => ModuleIntegration::count()
                ]);
                return ['success' => true, 'results' => []];
            }
            
            foreach ($modules as $module) {
                if (!$module->enabled || !$module->sync_endpoint) {
                    continue;
                }

                try {
                    $deleteUrl = rtrim($module->base_url, '/') . '/' . ltrim($module->sync_endpoint, '/');
                    
                    // Prepare delete data
                    $deleteData = [
                        'email' => $user->email,
                    ];
                    
                    // Add force_delete parameter if requested
                    if ($forceDelete) {
                        $deleteData['force_delete'] = true;
                    }

                    $response = \Illuminate\Support\Facades\Http::withHeaders([
                        'Authorization' => 'Bearer ' . $module->api_token,
                        'Content-Type' => 'application/json',
                    ])->delete($deleteUrl, $deleteData);

                    $responseBody = $response->body();
                    $responseData = json_decode($responseBody, true);
                    
                    $moduleResult = [
                        'module' => $module->name,
                        'status' => $response->status(),
                        'success' => $response->successful(),
                        'response' => $responseBody,
                        'parsed_response' => $responseData
                    ];
                    
                    $results[] = $moduleResult;
                    
                    if (!$response->successful()) {
                        $hasErrors = true;
                    }

                    Log::info('User deletion sync to module', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'module_name' => $module->name,
                        'force_delete' => $forceDelete,
                        'status' => $response->status(),
                        'response' => $responseBody
                    ]);

                } catch (\Exception $e) {
                    $hasErrors = true;
                    $moduleResult = [
                        'module' => $module->name,
                        'status' => 500,
                        'success' => false,
                        'error' => $e->getMessage(),
                        'response' => null
                    ];
                    $results[] = $moduleResult;
                    
                    Log::error('User deletion sync to module failed', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'module_name' => $module->name,
                        'force_delete' => $forceDelete,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            return [
                'success' => !$hasErrors,
                'results' => $results,
                'has_errors' => $hasErrors
            ];
            
        } catch (\Exception $e) {
            // Log the error and return failure
            Log::error('User deletion sync to modules failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'results' => [],
                'has_errors' => true,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync user module permissions to external modules
     */
    private function syncUserModulePermissions(User $user, array $modulePermissions, array $additionalFields)
    {
        try {
            $modules = ModuleIntegration::enabled()->get();
            
            foreach ($modules as $module) {
                // Only sync to modules that match the OMX Flow module
                if ($module->name === 'omx_flow' || $module->name === 'omx-flow' || $module->name === 'OMX FLOW') {
                    $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);
                    
                    // Send to external module with permissions and additional data
                    $result = $moduleIntegrationController->syncUserToModule($user, $module, $modulePermissions, $additionalFields);
                    
                    Log::info('Module permissions sync result', [
                        'user_id' => $user->id,
                        'module_name' => $module->name,
                        'permissions' => $modulePermissions,
                        'additional_fields' => $additionalFields,
                        'sync_successful' => $result
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Module permissions sync failed', [
                'user_id' => $user->id,
                'permissions' => $modulePermissions,
                'additional_fields' => $additionalFields,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function store(Request $request)
    {
        // Check permissions for different user types
        $hasAccess = false;
        if (\Auth::user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (\Auth::user()->type == 'staff') {
            $hasAccess = \Auth::user()->can('create white label users');
        } else {
            $hasAccess = \Auth::user()->can('create user');
        }

        if ($hasAccess) {
            $default_language = DB::table('settings')->select('value')->where('name', 'default_language')->where('created_by', '=', \Auth::user()->creatorId())->first();
            $objUser = \Auth::user()->creatorId();

if (\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff') {
                // System admin and staff can only create super admin users
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users',
                        'company_name' => 'required|max:255',
                        'company_description' => 'required|max:1000',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    return redirect()->back()->with('error', $messages->first());
                }

                $enableLogin = 0;
                if (!empty($request->password_switch) && $request->password_switch == 'on') {
                    $enableLogin = 1;
                    $validator = \Validator::make(
                        $request->all(), ['password' => 'required|min:6']
                    );

                    if ($validator->fails()) {
                        return redirect()->back()->with('error', $validator->errors()->first());
                    }
                }

                $userpassword = $request->input('password');
                $psw = $request->password;

                $user = new User();
                $user['name'] = $request->name;
                $user['email'] = $request->email;
                $user['password'] = !empty($userpassword) ? \Hash::make($userpassword) : null;
                $user['type'] = 'super admin';
                $user['lang'] = !empty($default_language) ? $default_language->value : 'en';
                $user['created_by'] = \Auth::user()->id;
                $user['email_verified_at'] = date('Y-m-d H:i:s');
                $user['is_enable_login'] = $enableLogin;

                // Save company name and description in user meta or related table if applicable
                // Assuming user model has company_name and company_description columns or use meta table
                $user['company_name'] = $request->company_name;
                $user['company_description'] = $request->company_description;

                $user->save();

                $role_r = Role::where('name', 'super admin')->first();
                if ($role_r) {
                    $user->assignRole($role_r);
                }

                // Handle module permissions for super admin
                if ($request->has('module_permissions') && is_array($request->module_permissions)) {
                    $user['module_permissions'] = json_encode($request->module_permissions);

                    if ($user->type == 'super admin') {
                        // Create or get system admin modules for the selected permissions
                        foreach ($request->module_permissions as $moduleKey) {
                            // Find or create the system admin module
                            $systemModule = SystemAdminModule::firstOrCreate([
                                'name' => $moduleKey,
                            ], [
                                'slug' => Str::slug($moduleKey),
                                'description' => 'Module: ' . $moduleKey,
                                'is_active' => true,
                                'sort_order' => SystemAdminModule::max('sort_order') + 1
                            ]);

                            // Create super admin module permission
                            SuperAdminModulePermission::create([
                                'super_admin_id' => $user->id,
                                'module_id' => $systemModule->id,
                                'has_access' => true,
                                'permissions' => [], // Default permissions
                                'granted_by' => \Auth::user()->id
                            ]);
                        }
                    }
                }

                // Sync user with enabled modules and send module permissions to external modules
                $this->syncUserToModules($user, $psw);
                
                // Sync module permissions to external modules
                $modulePermissions = $request->module_permissions ?? [];
                $this->syncUserModulePermissions($user, $modulePermissions, $request->only(['company_name', 'company_description']));

                // Send Email
                $setings = Utility::settings();
                if ($setings['new_user'] == 1) {
                    $user->password = $psw;
                    $user->type = 'super admin';

                    $userArr = [
                        'email' => $user->email,
                        'password' => $user->password,
                    ];
                    $resp = Utility::sendEmailTemplate('new_user', [$user->id => $user->email], $userArr);

                    return redirect()->route('users.index')->with('success', __('Super Admin successfully created.') . ((!empty($resp) && $resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
                }

                return redirect()->route('users.index')->with('success', __('Super Admin successfully created.'));

            } elseif (\Auth::user()->type == 'super admin') {
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $enableLogin = 0;
                if (!empty($request->password_switch) && $request->password_switch == 'on') {
                    $enableLogin = 1;
                    $validator = \Validator::make(
                        $request->all(), ['password' => 'required|min:6']
                    );

                    if ($validator->fails()) {
                        return redirect()->back()->with('error', $validator->errors()->first());
                    }
                }
                $userpassword = $request->input('password');
                $settings = Utility::settings();

                do {
                    $code = rand(100000, 999999);
                } while (User::where('referral_code', $code)->exists());

                $user = new User();
                $user['name'] = $request->name;
                $user['email'] = $request->email;
                $psw = $request->password;
                $user['password'] = !empty($userpassword) ? \Hash::make($userpassword) : null;
                $user['type'] = 'company';
                $user['default_pipeline'] = 1;
                $user['plan'] = 1;
                $user['lang'] = !empty($default_language) ? $default_language->value : 'en';
                $user['referral_code'] = $code;
                $user['created_by'] = \Auth::user()->creatorId();
                $user['plan'] = Plan::first()->id;
                if ($settings['email_verification'] == 'on') {
                    $user['email_verified_at'] = null;
                } else {
                    $user['email_verified_at'] = date('Y-m-d H:i:s');
                }
                $user['is_enable_login'] = $enableLogin;

                // Instead of saving module permissions from request, fetch from pricing plan
                $pricingPlan = \App\Models\PricingPlan::find($user['plan']);
                if ($pricingPlan && $pricingPlan->module_permissions) {
                    $user['module_permissions'] = $pricingPlan->module_permissions;
                } else {
                    $user['module_permissions'] = [];
                }

                $user->save();
                $role_r = Role::findByName('company');
                $user->assignRole($role_r);

                // Assign pricing plan permissions to Spatie permission system
                if ($pricingPlan && $pricingPlan->module_permissions) {
                    $user->assignPricingPlanPermissions();
                }

                $user->userDefaultDataRegister($user->id);
                $user->userWarehouseRegister($user->id);

                //default bank account for new company
                $user->userDefaultBankAccount($user->id);

                Utility::chartOfAccountTypeData($user->id);
                // default chart of account for new company
                Utility::chartOfAccountData1($user->id);

                Utility::pipeline_lead_deal_Stage($user->id);
                Utility::project_task_stages($user->id);
                Utility::labels($user->id);
                Utility::sources($user->id);
                Utility::jobStage($user->id);
                GenerateOfferLetter::defaultOfferLetterRegister($user->id);
                ExperienceCertificate::defaultExpCertificatRegister($user->id);
                JoiningLetter::defaultJoiningLetterRegister($user->id);
                NOC::defaultNocCertificateRegister($user->id);

                // Remove saving module permissions in SuperAdminModulePermission table for company

                // Sync user with enabled modules
                $this->syncUserToModules($user, $psw);
                
                // Sync module permissions to external modules for company
                if ($pricingPlan && $pricingPlan->module_permissions) {
                    // Check if OMX Flow permissions are included in the pricing plan
                    if (isset($pricingPlan->module_permissions['omx_flow'])) {
                        $omxFlowPermissions = $pricingPlan->module_permissions['omx_flow'];
                        $additionalFields = [
                            'company_name' => $user->name,
                            'company_description' => 'Company created via pricing plan integration',
                            'super_admin_email' => '<EMAIL>'
                        ];
                        $this->syncUserModulePermissions($user, $omxFlowPermissions, $additionalFields);
                    }
                }


            } else {
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users',
                        'role' => 'required',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    return redirect()->back()->with('error', $messages->first());
                }

                $enableLogin = 0;
                if (!empty($request->password_switch) && $request->password_switch == 'on') {
                    $enableLogin = 1;
                    $validator = \Validator::make(
                        $request->all(), ['password' => 'required|min:6']
                    );

                    if ($validator->fails()) {
                        return redirect()->back()->with('error', $validator->errors()->first());
                    }
                }

                $objUser = User::find($objUser);
                $user = User::find(\Auth::user()->created_by);
                
                if (!$objUser) {
                    return redirect()->back()->with('error', __('User not found.'));
                }
                
                $total_user = $objUser->countUsers();
                $plan = Plan::find($objUser->plan);
                $userpassword = $request->input('password');

                // Check if plan exists, if not assign default plan
                if (!$plan) {
                    // Try to get the first available plan as default
                    $defaultPlan = Plan::first();
                    if ($defaultPlan) {
                        $objUser->plan = $defaultPlan->id;
                        $objUser->save();
                        $plan = $defaultPlan;
                    } else {
                        return redirect()->back()->with('error', __('No plans available. Please contact administrator to set up plans.'));
                    }
                }

                if ($total_user < $plan->max_users || $plan->max_users == -1) {
                    $psw = $request->password;
                    $request['password'] = !empty($userpassword)?\Hash::make($userpassword) : null;
                    $request['type'] = 'Employee';
                    $request['lang'] = !empty($default_language) ? $default_language->value : 'en';
                    $request['created_by'] = \Auth::user()->creatorId();
                    $request['email_verified_at'] = date('Y-m-d H:i:s');
                    $request['is_enable_login'] = $enableLogin;

                    $user = User::create($request->all());
                    $user->assignRole('Employee');
                    \App\Models\Utility::employeeDetails($user->id, \Auth::user()->creatorId());

                } else {
                    return redirect()->back()->with('error', __('Your user limit is over, Please upgrade plan.'));
                }

                // Sync user with enabled modules
                $this->syncUserToModules($user, $psw);
            }
            // Send Email
            $setings = Utility::settings();
            if ($setings['new_user'] == 1) {

                $user->password = $psw;
                // Only set type from $role_r if it exists, otherwise set to 'Employee' for company-created users
                if (isset($role_r)) {
                    $user->type = $role_r->name;
                } else {
                    $user->type = 'Employee';
                }
                $user->userDefaultDataRegister($user->id);

                $userArr = [
                    'email' => $user->email,
                    'password' => $user->password,
                ];
                $resp = Utility::sendEmailTemplate('new_user', [$user->id => $user->email], $userArr);

                if (\Auth::user()->type == 'super admin') {
                    return redirect()->route('users.index')->with('success', __('Company successfully created.') . ((!empty($resp) && $resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));
                } else {
                    return redirect()->route('users.index')->with('success', __('User successfully created.') . ((!empty($resp) && $resp['is_success'] == false && !empty($resp['error'])) ? '<br> <span class="text-danger">' . $resp['error'] . '</span>' : ''));

                }
            }
            if (\Auth::user()->type == 'super admin') {
                return redirect()->route('users.index')->with('success', __('Company successfully created.'));
            } else {
                return redirect()->route('users.index')->with('success', __('User successfully created.'));

            }

        } else {
            return redirect()->back();
        }

    }
    public function show()
    {
        return redirect()->route('user.index');
    }

    public function edit($id)
    {
        $authUser = \Auth::user();

        // Check permissions for different user types
        $hasAccess = false;
        if ($authUser->type == 'system admin') {
            $hasAccess = true;
        } elseif ($authUser->type == 'staff') {
            $hasAccess = $authUser->can('edit white label users');
        } else {
            $hasAccess = $authUser->can('edit user');
        }

        if (!$hasAccess) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // System admin and staff can only edit super admin roles
        if ($authUser->type == 'system admin' || $authUser->type == 'staff') {
            $roles = Role::where('name', 'super admin')->get()->pluck('name', 'id');
        } else {
            $roles = Role::where('created_by', '=', $authUser->creatorId())->where('name', '!=', 'client')->get()->pluck('name', 'id');
        }

        $user = User::findOrFail($id);

        // System admin and staff can only edit super admin users
        if (($authUser->type == 'system admin' || $authUser->type == 'staff') && $user->type != 'super admin') {
            return redirect()->back()->with('error', __('Permission denied. You can only edit Super Admin users.'));
        }

        $user->customField = CustomField::getData($user, 'user');
        $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'user')->get();

        return view('user.edit', compact('user', 'roles', 'customFields'));

    }

    public function update(Request $request, $id)
    {
        // Check permissions for different user types
        $hasAccess = false;
        if (\Auth::user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (\Auth::user()->type == 'staff') {
            $hasAccess = \Auth::user()->can('edit white label users');
        } else {
            $hasAccess = \Auth::user()->can('edit user');
        }

        if ($hasAccess) {
            if (\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff') {
                $user = User::findOrFail($id);

                // System admin and staff can only update super admin users
                if ($user->type != 'super admin') {
                    return redirect()->back()->with('error', __('Permission denied. You can only update Super Admin users.'));
                }
                
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users,email,' . $id,
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    return redirect()->back()->with('error', $messages->first());
                }

                $input = $request->all();
                $input['type'] = 'super admin';
                
                // Track what fields were updated before saving
                $updatedFields = [];
                $originalData = $user->getOriginal();
                if ($originalData['name'] !== $input['name']) $updatedFields[] = 'name';
                if ($originalData['email'] !== $input['email']) $updatedFields[] = 'email';
                
                $user->fill($input)->save();
                CustomField::saveData($user, $request->customField);

                // Sync user updates to modules
                if (!empty($updatedFields)) {
                    $this->syncUserUpdateToModules($user, $updatedFields);
                }

                return redirect()->route('users.index')->with('success', __('Super Admin successfully updated.'));
            } elseif (\Auth::user()->type == 'super admin') {
                $user = User::findOrFail($id);
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users,email,' . $id,
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    return redirect()->back()->with('error', $messages->first());
                }

                $role = Role::findByName('company');
                $input = $request->all();
                $input['type'] = $role->name;

                // Track what fields were updated before saving
                $updatedFields = [];
                $originalData = $user->getOriginal();
                if ($originalData['name'] !== $input['name']) $updatedFields[] = 'name';
                if ($originalData['email'] !== $input['email']) $updatedFields[] = 'email';

                $user->fill($input)->save();
                CustomField::saveData($user, $request->customField);

                $roles[] = $role->id;
                $user->roles()->sync($roles);

                // Sync user updates to modules
                if (!empty($updatedFields)) {
                    $this->syncUserUpdateToModules($user, $updatedFields);
                }

                return redirect()->route('users.index')->with(
                    'success', 'company successfully updated.'
                );
            } else {
                $user = User::findOrFail($id);
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required|max:120',
                        'email' => 'required|email|unique:users,email,' . $id,
                        'role' => 'required',
                    ]
                );
                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    return redirect()->back()->with('error', $messages->first());
                }

                $role = Role::findById($request->role);
                $input = $request->all();
                $input['type'] = $role->name;
                $user->fill($input)->save();
                Utility::employeeDetailsUpdate($user->id, \Auth::user()->creatorId());
                CustomField::saveData($user, $request->customField);

                $roles[] = $request->role;
                $user->roles()->sync($roles);

                return redirect()->route('users.index')->with(
                    'success', 'User successfully updated.'
                );
            }
        } else {
            return redirect()->back();
        }
    }

    public function destroy($id)
    {
        if (\Auth::user()->can('delete user')) {
            if ($id == 2) {
                return redirect()->back()->with('error', __('You can not delete By default Company'));
            }

            $user = User::find($id);
            if ($user) {
                if (\Auth::user()->type == 'system admin') {
                    // System admin can only delete super admin users
                    if ($user->type != 'super admin') {
                        return redirect()->back()->with('error', __('Permission denied. You can only delete Super Admin users.'));
                    }

                    // Sync user deletion to modules before deleting
                    $syncResult = $this->syncUserDeleteToModules($user);
                    
                    if (!$syncResult['success']) {
                        // Build error message from module responses
                        $errorMessages = [];
                        foreach ($syncResult['results'] as $result) {
                            if (!$result['success']) {
                                $moduleError = $result['module'] . ': ';
                                if (isset($result['parsed_response']['message'])) {
                                    $moduleError .= $result['parsed_response']['message'];
                                } elseif (isset($result['error'])) {
                                    $moduleError .= $result['error'];
                                } else {
                                    $moduleError .= 'Unknown error (Status: ' . $result['status'] . ')';
                                }
                                $errorMessages[] = $moduleError;
                            }
                        }
                        
                        $errorMessage = __('Cannot delete user. Module errors: ') . implode('; ', $errorMessages);
                        return redirect()->back()->with('error', $errorMessage);
                    }
                    
                    $user->delete();
                    return redirect()->back()->with('success', __('Super Admin Successfully deleted'));
                }

                if (\Auth::user()->type == 'super admin') {
                    // Sync user deletion to modules before deleting
                    $syncResult = $this->syncUserDeleteToModules($user);
                    
                    if (!$syncResult['success']) {
                        // Build error message from module responses
                        $errorMessages = [];
                        foreach ($syncResult['results'] as $result) {
                            if (!$result['success']) {
                                $moduleError = $result['module'] . ': ';
                                if (isset($result['parsed_response']['message'])) {
                                    $moduleError .= $result['parsed_response']['message'];
                                } elseif (isset($result['error'])) {
                                    $moduleError .= $result['error'];
                                } else {
                                    $moduleError .= 'Unknown error (Status: ' . $result['status'] . ')';
                                }
                                $errorMessages[] = $moduleError;
                            }
                        }
                        
                        $errorMessage = __('Cannot delete company. Module errors: ') . implode('; ', $errorMessages);
                        return redirect()->back()->with('error', $errorMessage);
                    }

                    $transaction = ReferralTransaction::where('company_id' , $id)->delete();
                    $users = User::where('created_by', $id)->delete();
                    $employee = Employee::where('created_by', $id)->delete();
                    $user->delete();

                    return redirect()->back()->with('success', __('Company Successfully deleted'));
                }

                if (\Auth::user()->type == 'company') {
                    $delete_user = User::where(['id' => $user->id])->first();
                    if ($delete_user) {
                        // Sync user deletion to modules before deleting
                        $syncResult = $this->syncUserDeleteToModules($delete_user);
                        
                        if (!$syncResult['success']) {
                            // Build error message from module responses
                            $errorMessages = [];
                            foreach ($syncResult['results'] as $result) {
                                if (!$result['success']) {
                                    $moduleError = $result['module'] . ': ';
                                    if (isset($result['parsed_response']['message'])) {
                                        $moduleError .= $result['parsed_response']['message'];
                                    } elseif (isset($result['error'])) {
                                        $moduleError .= $result['error'];
                                    } else {
                                        $moduleError .= 'Unknown error (Status: ' . $result['status'] . ')';
                                    }
                                    $errorMessages[] = $moduleError;
                                }
                            }
                            
                            $errorMessage = __('Cannot delete user. Module errors: ') . implode('; ', $errorMessages);
                            return redirect()->back()->with('error', $errorMessage);
                        }
                        
                        $employee = Employee::where(['user_id' => $user->id])->delete();
                        $delete_user->delete();

                        return redirect()->route('users.index')->with('success', __('User successfully deleted .'));
                    } else {
                        return redirect()->back()->with('error', __('Something is wrong.'));
                    }
                }
                return redirect()->route('users.index')->with('success', __('User successfully deleted .'));
            } else {
                return redirect()->back()->with('error', __('Something is wrong.'));
            }
        } else {
            return redirect()->back();
        }
    }

    /**
     * Force delete user - bypasses module dependency checks
     */
    public function forceDestroy($id)
    {
        if (\Auth::user()->can('delete user')) {
            if ($id == 2) {
                return redirect()->back()->with('error', __('You can not delete By default Company'));
            }

            $user = User::find($id);
            if ($user) {
                if (\Auth::user()->type == 'system admin') {
                    // System admin can only delete super admin users
                    if ($user->type != 'super admin') {
                        return redirect()->back()->with('error', __('Permission denied. You can only delete Super Admin users.'));
                    }

                    // Force sync user deletion to modules
                    $syncResult = $this->syncUserDeleteToModules($user, true);
                    
                    $user->delete();
                    
                    if ($syncResult['has_errors']) {
                        return redirect()->back()->with('success', __('Super Admin force deleted successfully. Some modules may have reported errors but deletion proceeded.'));
                    }
                    
                    return redirect()->back()->with('success', __('Super Admin Successfully deleted'));
                }

                if (\Auth::user()->type == 'super admin') {
                    // Force sync user deletion to modules
                    $syncResult = $this->syncUserDeleteToModules($user, true);

                    $transaction = ReferralTransaction::where('company_id' , $id)->delete();
                    $users = User::where('created_by', $id)->delete();
                    $employee = Employee::where('created_by', $id)->delete();
                    $user->delete();

                    if ($syncResult['has_errors']) {
                        return redirect()->back()->with('success', __('Company force deleted successfully. Some modules may have reported errors but deletion proceeded.'));
                    }

                    return redirect()->back()->with('success', __('Company Successfully deleted'));
                }

                if (\Auth::user()->type == 'company') {
                    $delete_user = User::where(['id' => $user->id])->first();
                    if ($delete_user) {
                        // Force sync user deletion to modules
                        $syncResult = $this->syncUserDeleteToModules($delete_user, true);
                        
                        $employee = Employee::where(['user_id' => $user->id])->delete();
                        $delete_user->delete();

                        if ($syncResult['has_errors']) {
                            return redirect()->route('users.index')->with('success', __('User force deleted successfully. Some modules may have reported errors but deletion proceeded.'));
                        }

                        return redirect()->route('users.index')->with('success', __('User successfully deleted .'));
                    } else {
                        return redirect()->back()->with('error', __('Something is wrong.'));
                    }
                }
                return redirect()->route('users.index')->with('success', __('User successfully deleted .'));
            } else {
                return redirect()->back()->with('error', __('Something is wrong.'));
            }
        } else {
            return redirect()->back();
        }
    }

    public function profile()
    {
        $userDetail = \Auth::user();
        $userDetail->customField = CustomField::getData($userDetail, 'user');
        $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'user')->get();

        return view('user.profile', compact('userDetail', 'customFields'));
    }

    public function editprofile(Request $request)
    {
        $userDetail = \Auth::user();
        $user = User::findOrFail($userDetail['id']);

        $validator = \Validator::make(
            $request->all(), [
                'name' => 'required|max:120',
                'email' => 'required|email|unique:users,email,' . $userDetail['id'],
            ]
        );
        if ($validator->fails()) {
            $messages = $validator->getMessageBag();
            return redirect()->back()->with('error', $messages->first());
        }

        if ($request->hasFile('profile')) {
            $filenameWithExt = $request->file('profile')->getClientOriginalName();
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            $extension = $request->file('profile')->getClientOriginalExtension();
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;

            $settings = Utility::getStorageSetting();
            if ($settings['storage_setting'] == 'local') {
                $dir = 'uploads/avatar/';
            } else {
                $dir = 'uploads/avatar';
            }

            $image_path = $dir . $userDetail['avatar'];

            if (File::exists($image_path)) {
                File::delete($image_path);
            }

            $url = '';
            $path = Utility::upload_file($request, 'profile', $fileNameToStore, $dir, []);
            if ($path['flag'] == 1) {
                $url = $path['url'];
            } else {
                return redirect()->route('profile', \Auth::user()->id)->with('error', __($path['msg']));
            }
        }

        if (!empty($request->profile)) {
            $user['avatar'] = $fileNameToStore;
        }
        $user['name'] = $request['name'];
        $user['email'] = $request['email'];
        $user->save();
        CustomField::saveData($user, $request->customField);

        return redirect()->route('profile', $user)->with(
            'success', 'Profile successfully updated.'
        );
    }

    public function updatePassword(Request $request)
    {

        if (Auth::Check()) {

            $validator = \Validator::make(
                $request->all(), [
                    'old_password' => 'required',
                    'password' => 'required|min:6',
                    'password_confirmation' => 'required|same:password',
                ]
            );
            if ($validator->fails()) {
                $messages = $validator->getMessageBag();
                return redirect()->back()->with('error', $messages->first());
            }

            $objUser = Auth::user();
            $request_data = $request->All();
            $current_password = $objUser->password;
            if (Hash::check($request_data['old_password'], $current_password)) {
                $user_id = Auth::User()->id;
                $obj_user = User::find($user_id);
                $obj_user->password = Hash::make($request_data['password']);
                $obj_user->save();

                return redirect()->route('profile', $objUser->id)->with('success', __('Password successfully updated.'));
            } else {
                return redirect()->route('profile', $objUser->id)->with('error', __('Please enter correct current password.'));
            }
        } else {
            return redirect()->route('profile', \Auth::user()->id)->with('error', __('Something is wrong.'));
        }
    }
    // User To do module
    public function todo_store(Request $request)
    {
        $request->validate(
            ['title' => 'required|max:120']
        );

        $post = $request->all();
        $post['user_id'] = Auth::user()->id;
        $todo = UserToDo::create($post);

        $todo->updateUrl = route(
            'todo.update', [
                $todo->id,
            ]
        );
        $todo->deleteUrl = route(
            'todo.destroy', [
                $todo->id,
            ]
        );

        return $todo->toJson();
    }

    public function todo_update($todo_id)
    {
        $user_todo = UserToDo::find($todo_id);
        if ($user_todo->is_complete == 0) {
            $user_todo->is_complete = 1;
        } else {
            $user_todo->is_complete = 0;
        }
        $user_todo->save();
        return $user_todo->toJson();
    }

    public function todo_destroy($id)
    {
        $todo = UserToDo::find($id);
        $todo->delete();

        return true;
    }

    // change mode 'dark or light'
    public function changeMode()
    {
        $usr = \Auth::user();
        if ($usr->mode == 'light') {
            $usr->mode = 'dark';
            $usr->dark_mode = 1;
        } else {
            $usr->mode = 'light';
            $usr->dark_mode = 0;
        }
        $usr->save();

        return redirect()->back();
    }

    public function upgradePlan($user_id)
    {
        $user = User::find($user_id);
        $plans = Plan::get();
        $admin_payment_setting = Utility::getAdminPaymentSetting();

        return view('user.plan', compact('user', 'plans', 'admin_payment_setting'));
    }
    public function activePlan($user_id, $plan_id)
    {

        $plan = Plan::find($plan_id);
        if (!$plan) {
            return redirect()->back()->with('error', __('Plan not found.'));
        }
        if($plan->is_disable == 0)
        {
            return redirect()->back()->with('error', __('You are unable to upgrade this plan because it is disabled.'));
        }

        $user = User::find($user_id);
        $assignPlan = $user->assignPlan($plan_id, $user_id);
        if ($assignPlan['is_success'] == true && !empty($plan)) {
            $orderID = strtoupper(str_replace('.', '', uniqid('', true)));
            Order::create(
                [
                    'order_id' => $orderID,
                    'name' => null,
                    'card_number' => null,
                    'card_exp_month' => null,
                    'card_exp_year' => null,
                    'plan_name' => $plan->name,
                    'plan_id' => $plan->id,
                    'price' => $plan->price,
                    'price_currency' => isset(\Auth::user()->planPrice()['currency'])?\Auth::user()->planPrice()['currency'] : '',
                    'txn_id' => '',
                    'payment_status' => 'success',
                    'receipt' => null,
                    'user_id' => $user->id,
                ]
            );

            return redirect()->back()->with('success', 'Plan successfully upgraded.');
        } else {
            return redirect()->back()->with('error', 'Plan fail to upgrade.');
        }

    }

    public function userPassword($id)
    {
        $eId = \Crypt::decrypt($id);
        $user = User::find($eId);

        return view('user.reset', compact('user'));

    }

    public function userPasswordReset(Request $request, $id)
    {
        $validator = \Validator::make(
            $request->all(), [
                'password' => 'required|confirmed|same:password_confirmation',
            ]
        );

        if ($validator->fails()) {
            $messages = $validator->getMessageBag();

            return redirect()->back()->with('error', $messages->first());
        }

        $user = User::where('id', $id)->first();
        $user->forceFill([
            'password' => Hash::make($request->password),
            'is_enable_login' => 1,
        ])->save();

        if(\Auth::user()->type == 'super admin')
        {
        return redirect()->route('users.index')->with(
            'success', 'Company Password successfully updated.'
        );
    }
    else
    {
        return redirect()->route('users.index')->with(
            'success', 'User Password successfully updated.'
        );
    }

    }

    //start for user login details
    public function userLog(Request $request)
    {
        $filteruser = User::where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
        $filteruser->prepend('Select User', '');

        $query = DB::table('login_details')
            ->join('users', 'login_details.user_id', '=', 'users.id')
            ->select(DB::raw('login_details.*, users.id as user_id , users.name as user_name , users.email as user_email ,users.type as user_type'))
            ->where(['login_details.created_by' => \Auth::user()->id]);

        if (!empty($request->month)) {
            $query->whereMonth('date', date('m', strtotime($request->month)));
            $query->whereYear('date', date('Y', strtotime($request->month)));
        } else {
            $query->whereMonth('date', date('m'));
            $query->whereYear('date', date('Y'));
        }

        if (!empty($request->users)) {
            $query->where('user_id', '=', $request->users);
        }
        $userdetails = $query->get();
        $last_login_details = LoginDetail::where('created_by', \Auth::user()->creatorId())->get();

        return view('user.userlog', compact('userdetails', 'last_login_details', 'filteruser'));
    }

    public function userLogView($id)
    {
        $users = LoginDetail::find($id);

        return view('user.userlogview', compact('users'));
    }

    public function userLogDestroy($id)
    {
        $users = LoginDetail::where('user_id', $id)->delete();
        return redirect()->back()->with('success', 'User successfully deleted.');
    }

    public function LoginWithCompany(Request $request, User $user, $id)
    {
        $user = User::find($id);
        if ($user && auth()->check()) {
            if (auth()->user()->type == 'system admin' && $user->type == 'super admin') {
                // System admin logging in as super admin
                Impersonate::take($request->user(), $user);
                return redirect('/dashboard');
            } elseif (auth()->user()->type == 'system admin' && $user->type == 'company') {
                // System admin logging in as company (NEW - this was missing)
                // Verify the company was created by this system admin
                if ($user->created_by == auth()->user()->id) {
                    Impersonate::take($request->user(), $user);
                    return redirect('/account-dashboard');
                } else {
                    return redirect()->back()->with('error', __('Permission denied. You can only login as companies you created.'));
                }
            } elseif (auth()->user()->type == 'super admin') {
                // Super admin logging in as company
                Impersonate::take($request->user(), $user);
                return redirect('/account-dashboard');
            }
        }
        return redirect()->back()->with('error', __('Permission denied.'));
    }

    public function ExitCompany(Request $request)
    {
        \Auth::user()->leaveImpersonation($request->user());
        return redirect('/dashboard');
    }

    public function companyInfo(Request $request, $id)
    {
        $user = User::find($request->id);
        $status = $user->delete_status;
        $userData = User::where('created_by', $id)->where('type', '!=', 'client')->selectRaw('COUNT(*) as total_users, SUM(CASE WHEN is_disable = 0 THEN 1 ELSE 0 END) as disable_users, SUM(CASE WHEN is_disable = 1 THEN 1 ELSE 0 END) as active_users')->first();

        return view('user.company_info', compact('userData', 'id', 'status'));
    }

    public function userUnable(Request $request)
    {
        User::where('id', $request->id)->update(['is_disable' => $request->is_disable]);
        $userData = User::where('created_by', $request->company_id)->where('type', '!=', 'client')->selectRaw('COUNT(*) as total_users, SUM(CASE WHEN is_disable = 0 THEN 1 ELSE 0 END) as disable_users, SUM(CASE WHEN is_disable = 1 THEN 1 ELSE 0 END) as active_users')->first();

        if ($request->is_disable == 1) {

            return response()->json(['success' => __('User successfully unable.'), 'userData' => $userData]);

        } else {
            return response()->json(['success' => __('User successfully disable.'), 'userData' => $userData]);
        }
    }

    public function LoginManage($id)
    {
        $eId = \Crypt::decrypt($id);
        $user = User::find($eId);
        $authUser = \Auth::user();

        if ($user->is_enable_login == 1) {
            $user->is_enable_login = 0;
            $user->save();

            if($authUser->type == 'super admin')
            {
                return redirect()->back()->with('success', __('Company login disable successfully.'));
            }
            else
            {
                return redirect()->back()->with('success', __('User login disable successfully.'));
            }
        } else {
            $user->is_enable_login = 1;
            $user->save();
            if($authUser->type == 'super admin')
            {
                return redirect()->back()->with('success', __('Company login enable successfully.'));
            }
            else
            {
                return redirect()->back()->with('success', __('User login enable successfully.'));
            }
        }
    }
}
