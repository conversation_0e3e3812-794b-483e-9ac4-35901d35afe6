<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CrmWebhookDispatcher;

class TestAllLeadWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:test-all-lead-actions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all lead webhook actions';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing all lead webhook actions...');
        
        $webhookDispatcher = new CrmWebhookDispatcher();
        
        // Sample lead data
        $sampleLead = (object) [
            'id' => 999,
            'name' => 'Test Lead',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'pipeline_id' => 1,
            'stage_id' => 1,
            'user_id' => 1,
            'created_at' => now(),
        ];
        
        // Sample deal data for conversion
        $sampleDeal = (object) [
            'id' => 888,
            'name' => 'Converted Deal',
            'price' => 5000.00,
            'pipeline_id' => 1,
            'stage_id' => 2,
            'created_at' => now(),
        ];
        
        // Sample task data
        $sampleTask = (object) [
            'id' => 777,
            'lead_id' => 999,
            'name' => 'Test Task',
            'date' => now()->toDateString(),
            'priority' => 2,
            'status' => 1,
            'assign_to' => 1,
            'created_at' => now(),
        ];
        
        $actions = [
            'Lead Created' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadCreated($sampleLead);
            },
            'Lead Updated' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadUpdated($sampleLead, ['name' => 'Updated Name']);
            },
            'Lead Deleted' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadDeleted($sampleLead);
            },
            'Lead Stage Changed' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatchLeadStageChanged($sampleLead, 1, 2);
            },
            'Lead Converted to Deal' => function() use ($webhookDispatcher, $sampleLead, $sampleDeal) {
                return $webhookDispatcher->dispatchLeadConvertedToDeal($sampleLead, $sampleDeal);
            },
            'Lead Assigned' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatch('crm.lead_assigned', $sampleLead, ['assigned_user_id' => 2]);
            },
            'Lead Unassigned' => function() use ($webhookDispatcher, $sampleLead) {
                return $webhookDispatcher->dispatch('crm.lead_unassigned', $sampleLead, ['previous_user_id' => 1]);
            },
            'Lead Task Created' => function() use ($webhookDispatcher, $sampleTask) {
                return $webhookDispatcher->dispatchLeadTaskCreated($sampleTask);
            },
            'Lead Task Completed' => function() use ($webhookDispatcher, $sampleTask) {
                return $webhookDispatcher->dispatchLeadTaskCompleted($sampleTask);
            },
        ];
        
        $this->info('Sending test webhooks for all lead actions...');
        $this->newLine();
        
        foreach ($actions as $actionName => $actionCallback) {
            $this->info("Testing: {$actionName}");
            
            try {
                $result = $actionCallback();
                
                $successCount = 0;
                $failureCount = 0;
                
                foreach ($result as $moduleName => $moduleResult) {
                    if ($moduleResult['success'] ?? false) {
                        $successCount++;
                        $this->line("  ✅ {$moduleName}: SUCCESS");
                    } else {
                        $failureCount++;
                        $error = $moduleResult['error'] ?? 'Unknown error';
                        $this->line("  ❌ {$moduleName}: FAILED - {$error}");
                    }
                }
                
                $this->line("  📊 Summary: {$successCount} successful, {$failureCount} failed");
                
            } catch (\Exception $e) {
                $this->error("  💥 Exception: {$e->getMessage()}");
            }
            
            $this->newLine();
        }
        
        $this->info('All lead webhook tests completed!');
        $this->info('Check your webhook.site URL to see the payloads: https://webhook.site/4699cee0-4292-4825-8a0c-a281e54d9a68');
        
        return 0;
    }
}
