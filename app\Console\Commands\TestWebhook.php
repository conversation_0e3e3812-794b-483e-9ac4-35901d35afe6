<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CrmWebhookDispatcher;
use App\Models\Lead;

class TestWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:test-lead-created';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test webhook for lead created event';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing webhook for lead created event...');
        
        // Create sample lead data
        $sampleLead = (object) [
            'id' => 999,
            'name' => 'Test Lead',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'pipeline_id' => 1,
            'stage_id' => 1,
            'created_at' => now(),
        ];
        
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $result = $webhookDispatcher->dispatchLeadCreated($sampleLead);
            
            $this->info('Webhook dispatch completed!');
            $this->info('Results:');
            
            foreach ($result as $moduleName => $moduleResult) {
                $status = $moduleResult['success'] ?? false ? 'SUCCESS' : 'FAILED';
                $this->line("- {$moduleName}: {$status}");
                
                if (!($moduleResult['success'] ?? false)) {
                    $error = $moduleResult['error'] ?? 'Unknown error';
                    $this->error("  Error: {$error}");
                }
            }
            
        } catch (\Exception $e) {
            $this->error('Webhook test failed: ' . $e->getMessage());
            $this->error('Trace: ' . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}
