<?php

namespace App\Http\Controllers;

use App\Models\PersonalTask;
use App\Models\PersonalTaskComment;
use App\Models\PersonalTaskFile;
use App\Models\PersonalTaskChecklist;
use App\Models\PersonalTaskTimeTracking;
use App\Models\ProjectTask;
use App\Models\TaskStage;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class PersonalTaskController extends Controller
{
    /**
     * Display a listing of the tasks.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (Auth::user()->can('manage personal task')) {
            $stages = TaskStage::orderBy('order')->where('created_by', Auth::user()->creatorId())->get();
            foreach ($stages as $stage) {
                $stageClass[] = 'task-list-' . $stage->id;
                
                // Get personal tasks for this stage
                $personalTasks = PersonalTask::where('stage_id', $stage->id)
                    ->where(function ($query) {
                        $query->where('created_by', Auth::user()->id)
                            ->orWhereRaw("find_in_set('" . Auth::user()->id . "',assign_to)");
                    })
                    ->orderBy('order')
                    ->get();
                
                $stage['tasks'] = $personalTasks;
            }
            
            return view('personal_task.index', compact('stages', 'stageClass'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new task.
     *
     * @param  int  $stage_id (optional)
     * @return \Illuminate\Http\Response
     */
    public function create($stage_id = null)
    {
        // Check permission first
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Debug: Log the create request
        Log::info('Personal Task Create Request', [
            'user_id' => Auth::id(),
            'stage_id' => $stage_id,
            'request_url' => request()->url()
        ]);

        try {
            $currentUser = Auth::user();

            // Get the creator ID safely - use a more robust approach
            $creatorId = $currentUser->created_by ?? $currentUser->id;

            Log::info('Personal Task Create - User Info', [
                'current_user_id' => $currentUser->id,
                'creator_id' => $creatorId,
                'user_type' => $currentUser->type ?? 'unknown'
            ]);

            // If no stage_id provided, get the first available stage
            if (!$stage_id) {
                $firstStage = TaskStage::where('created_by', $creatorId)
                    ->orderBy('order')
                    ->first();

                // If no stages exist, create default ones
                if (!$firstStage) {
                    $defaultStages = ['To Do', 'In Progress', 'Review', 'Done'];
                    foreach ($defaultStages as $key => $stageName) {
                        TaskStage::create([
                            'name' => $stageName,
                            'order' => $key,
                            'created_by' => $creatorId,
                        ]);
                    }
                    // Get the first stage after creating defaults
                    $firstStage = TaskStage::where('created_by', $creatorId)
                        ->orderBy('order')
                        ->first();
                }

                $stage_id = $firstStage ? $firstStage->id : 1; // Fallback to 1 if still null
            }

            // Get users from the same company - simplified and robust approach
            // Determine the company owner ID based on user type
            if ($currentUser->type == 'company' || $currentUser->type == 'super admin') {
                $companyOwnerId = $currentUser->id;
            } else {
                $companyOwnerId = $currentUser->created_by ?? $currentUser->id;
            }

            Log::info('Personal Task Create - Company Info', [
                'current_user_id' => $currentUser->id,
                'current_user_type' => $currentUser->type,
                'company_owner_id' => $companyOwnerId,
                'system_admin_company_id' => $currentUser->system_admin_company_id ?? 'none'
            ]);

            // Get users based on company structure
            if ($currentUser->system_admin_company_id) {
                // For system admin company users, get users from the same system admin company
                $users = User::where('system_admin_company_id', $currentUser->system_admin_company_id)
                    ->where('type', '!=', 'client')
                    ->where('is_active', 1)
                    ->orderBy('name', 'asc')
                    ->get();
            } else {
                // For regular company users, get users created by the same company owner
                $users = User::where(function($query) use ($companyOwnerId, $currentUser) {
                    // Include users created by the company owner
                    $query->where('created_by', $companyOwnerId)
                          // Include the company owner themselves
                          ->orWhere('id', $companyOwnerId);
                })
                ->where('type', '!=', 'client')
                ->where('type', '!=', 'super admin') // Exclude super admins from other companies
                ->where('is_active', 1)
                ->orderBy('name', 'asc')
                ->get();
            }

            // Ensure current user is always included
            $currentUserInList = $users->where('id', $currentUser->id)->first();
            if (!$currentUserInList) {
                $users = $users->push($currentUser);
            }

            // If still no users found, at least include current user
            if ($users->isEmpty()) {
                $users = collect([$currentUser]);
            }

            return view('personal_task.create', compact('stage_id', 'users'));

        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Personal Task Create Error', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback: simple approach
            $currentUser = Auth::user();
            $stage_id = $stage_id ?? 1;
            $users = User::where('is_active', 1)
                        ->where('type', '!=', 'client')
                        ->orderBy('name', 'asc')
                        ->get();

            if ($users->isEmpty()) {
                $users = collect([$currentUser]);
            }

            return view('personal_task.create', compact('stage_id', 'users'));
        }
    }

    /**
     * Store a newly created task in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $stage_id (optional)
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $stage_id = null)
    {
        if (Auth::user()->can('create personal task')) {
            $validator = Validator::make(
                $request->all(), [
                    'name' => 'required',
                    'priority' => 'required',
                    'status' => '', // <-- add this line
                ]
            );

            if ($validator->fails()) {
                if ($request->ajax()) {
                    return response()->json([
                        'is_success' => false,
                        'error' => Utility::errorFormat($validator->getMessageBag()),
                        'message' => Utility::errorFormat($validator->getMessageBag())
                    ], 422);
                }
                return redirect()->back()->with('error', Utility::errorFormat($validator->getMessageBag()));
            }

            $post = $request->all();

            // If no stage_id provided or it's null, get the first available stage
            if (!$stage_id) {
                $currentUser = Auth::user();
                $creatorId = $currentUser->created_by ?? $currentUser->id;

                $firstStage = TaskStage::where('created_by', $creatorId)
                    ->orderBy('order')
                    ->first();

                // If no stages exist, create default ones
                if (!$firstStage) {
                    $defaultStages = ['To Do', 'In Progress', 'Review', 'Done'];
                    foreach ($defaultStages as $key => $stageName) {
                        TaskStage::create([
                            'name' => $stageName,
                            'order' => $key,
                            'created_by' => $creatorId,
                        ]);
                    }
                    // Get the first stage after creating defaults
                    $firstStage = TaskStage::where('created_by', $creatorId)
                        ->orderBy('order')
                        ->first();
                }

                $stage_id = $firstStage ? $firstStage->id : 1;
            }

            $post['stage_id'] = $stage_id;
            $post['created_by'] = Auth::user()->id;
            $post['priority_color'] = PersonalTask::$priority_color[$request->priority];

            // Handle assign_to field - ensure it's properly processed
            if (isset($request->assign_to) && !empty($request->assign_to) && is_array($request->assign_to)) {
                // Remove any empty values and ensure all values are integers
                $assignedUsers = array_filter($request->assign_to, function($userId) {
                    return !empty($userId) && is_numeric($userId);
                });

                if (!empty($assignedUsers)) {
                    $post['assign_to'] = implode(',', $assignedUsers);
                } else {
                    // If no valid users selected, assign to current user
                    $post['assign_to'] = Auth::user()->id;
                }
            } else {
                // If no assignment provided, assign to current user
                $post['assign_to'] = Auth::user()->id;
            }

            $task = PersonalTask::create($post);

            // Send notification to assigned users
            if (!empty($request->assign_to) && is_array($request->assign_to)) {
                foreach ($request->assign_to as $userId) {
                    if ($userId != Auth::user()->id) {
                        $user = User::find($userId);
                        if ($user) {
                            // Send notification logic here
                            // You can add notification logic here later
                        }
                    }
                }
            }

            // Debug: Log the assignment for verification
            Log::info('Personal Task Created', [
                'task_id' => $task->id,
                'task_name' => $task->name,
                'assigned_to' => $task->assign_to,
                'assigned_users' => $request->assign_to ?? [],
                'created_by' => Auth::user()->id
            ]);

            // Check if this is an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'is_success' => true,
                    'success' => __('Task created successfully.'),
                    'message' => __('Task created successfully.'),
                    'redirect' => route('my-tasks.index')
                ]);
            }

            return redirect()->route('my-tasks.index')->with('success', __('Task created successfully.'));
        } else {
            if ($request->ajax()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                    'message' => __('Permission Denied.')
                ], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified task.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (Auth::user()->can('view personal task')) {
            $task = PersonalTask::find($id);
            
            if ($task && ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))) {
                return view('personal_task.show', compact('task'));
            } else {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for editing the specified task.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        if (Auth::user()->can('edit personal task')) {
            $task = PersonalTask::find($id);
            
            if ($task && ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))) {
                // Get users from the same company using the same logic as create method
                $currentUser = Auth::user();

                // Determine the company owner ID based on user type (same as create method)
                if ($currentUser->type == 'company' || $currentUser->type == 'super admin') {
                    $companyOwnerId = $currentUser->id;
                } else {
                    $companyOwnerId = $currentUser->created_by ?? $currentUser->id;
                }

                // Get users based on company structure
                if ($currentUser->system_admin_company_id) {
                    // For system admin company users, get users from the same system admin company
                    $users = User::where('system_admin_company_id', $currentUser->system_admin_company_id)
                        ->where('type', '!=', 'client')
                        ->where('is_active', 1)
                        ->orderBy('name', 'asc')
                        ->get();
                } else {
                    // For regular company users, get users created by the same company owner
                    $users = User::where(function($query) use ($companyOwnerId) {
                        // Include users created by the company owner
                        $query->where('created_by', $companyOwnerId)
                              // Include the company owner themselves
                              ->orWhere('id', $companyOwnerId);
                    })
                    ->where('type', '!=', 'client')
                    ->where('type', '!=', 'super admin') // Exclude super admins from other companies
                    ->where('is_active', 1)
                    ->orderBy('name', 'asc')
                    ->get();
                }

                // Ensure current user is always included
                $currentUserInList = $users->where('id', $currentUser->id)->first();
                if (!$currentUserInList) {
                    $users = $users->push($currentUser);
                }

                // If still no users found, at least include current user
                if ($users->isEmpty()) {
                    $users = collect([$currentUser]);
                }

                return view('personal_task.edit', compact('task', 'users'));
            } else {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Update the specified task in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        if (Auth::user()->can('edit personal task')) {
            $task = PersonalTask::find($id);
            
            if ($task && ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))) {
                $validator = Validator::make(
                    $request->all(), [
                        'name' => 'required',
                        'priority' => 'required',
                        'status' => 'required', // <-- add this line
                    ]
                );

                if ($validator->fails()) {
                    if ($request->ajax()) {
                        return response()->json([
                            'is_success' => false,
                            'error' => Utility::errorFormat($validator->getMessageBag()),
                            'message' => Utility::errorFormat($validator->getMessageBag())
                        ], 422);
                    }
                    return redirect()->back()->with('error', Utility::errorFormat($validator->getMessageBag()));
                }

                $post = $request->all();
                $post['priority_color'] = PersonalTask::$priority_color[$request->priority];

                // Handle assign_to field - ensure it's properly processed
                if (isset($request->assign_to) && !empty($request->assign_to) && is_array($request->assign_to)) {
                    // Remove any empty values and ensure all values are integers
                    $assignedUsers = array_filter($request->assign_to, function($userId) {
                        return !empty($userId) && is_numeric($userId);
                    });

                    if (!empty($assignedUsers)) {
                        $post['assign_to'] = implode(',', $assignedUsers);
                    } else {
                        // If no valid users selected, keep current user assigned
                        $post['assign_to'] = Auth::user()->id;
                    }
                } else {
                    // If no assignment provided, keep current user assigned
                    $post['assign_to'] = Auth::user()->id;
                }

                $task->update($post);

                // Check if this is an AJAX request
                if ($request->ajax()) {
                    return response()->json([
                        'is_success' => true,
                        'success' => __('Task updated successfully.'),
                        'message' => __('Task updated successfully.'),
                        'redirect' => route('my-tasks.index')
                    ]);
                }

                return redirect()->route('my-tasks.index')->with('success', __('Task updated successfully.'));
            } else {
                if ($request->ajax()) {
                    return response()->json([
                        'is_success' => false,
                        'error' => __('Task not found or permission denied.'),
                        'message' => __('Task not found or permission denied.')
                    ], 404);
                }
                return redirect()->back()->with('error', __('Task not found or permission denied.'));
            }
        } else {
            if ($request->ajax()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Permission Denied.'),
                    'message' => __('Permission Denied.')
                ], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified task from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if (request()->ajax()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Authentication required.'),
                    'message' => __('Authentication required.')
                ], 401);
            }
            return redirect()->route('login');
        }

        $task = PersonalTask::find($id);

        if (!$task) {
            if (request()->ajax()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('Task not found.'),
                    'message' => __('Task not found.')
                ], 404);
            }
            return redirect()->back()->with('error', __('Task not found.'));
        }

        // Check if user owns the task or is assigned to it
        if ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id)) {
            try {
                $task->delete();

                // Check if this is an AJAX request
                if (request()->ajax()) {
                    return response()->json([
                        'is_success' => true,
                        'success' => __('Task deleted successfully.'),
                        'message' => __('Task deleted successfully.'),
                        'redirect' => route('my-tasks.index')
                    ]);
                }

                return redirect()->route('my-tasks.index')->with('success', __('Task deleted successfully.'));
            } catch (\Exception $e) {
                Log::error('Error deleting personal task', [
                    'task_id' => $id,
                    'user_id' => Auth::id(),
                    'error' => $e->getMessage()
                ]);

                if (request()->ajax()) {
                    return response()->json([
                        'is_success' => false,
                        'error' => __('Failed to delete task.'),
                        'message' => __('Failed to delete task.')
                    ], 500);
                }
                return redirect()->back()->with('error', __('Failed to delete task.'));
            }
        } else {
            if (request()->ajax()) {
                return response()->json([
                    'is_success' => false,
                    'error' => __('You do not have permission to delete this task.'),
                    'message' => __('You do not have permission to delete this task.')
                ], 403);
            }
            return redirect()->back()->with('error', __('You do not have permission to delete this task.'));
        }
    }

    /**
     * Display the task board view.
     *
     * @param  string  $view
     * @return \Illuminate\Http\Response
     */
    public function taskBoard($view)
    {
        if (Auth::user()->can('manage personal task')) {
            if ($view == 'list') {
                return view('personal_task.taskboard', compact('view'));
            } else {
                $tasks = PersonalTask::where(function ($query) {
                    $query->where('created_by', Auth::user()->id)
                        ->orWhereRaw("find_in_set('" . Auth::user()->id . "',assign_to)");
                })->get();

                return view('personal_task.grid', compact('tasks', 'view'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the task board view with filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function taskboardView(Request $request)
    {
        if (Auth::user()->can('manage personal task')) {
            if ($request->ajax() && $request->has('view') && $request->has('sort')) {
                $sort = explode('-', $request->sort);
                $tasks = PersonalTask::where(function ($query) {
                    $query->where('created_by', Auth::user()->id)
                        ->orWhereRaw("find_in_set('" . Auth::user()->id . "',assign_to)");
                })->orderBy($sort[0], $sort[1]);

                // Apply keyword filter
                if (!empty($request->keyword)) {
                    $tasks->where('name', 'LIKE', $request->keyword . '%');
                }

                // Apply status filter
                if (!empty($request->status)) {
                    if (is_array($request->status) && in_array('see_my_tasks', $request->status)) {
                        $tasks->whereRaw("find_in_set('" . Auth::user()->id . "',assign_to)");
                    } else {
                        $tasks->whereIn('priority', $request->status);
                    }
                }

                $tasks = $tasks->get();

                if ($request->view == 'list') {
                    return response()->json([
                        'html' => view('personal_task.list', compact('tasks'))->render(),
                    ]);
                } else {
                    return response()->json([
                        'html' => view('personal_task.grid_content', compact('tasks'))->render(),
                    ]);
                }
            }
        } else {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Get all tasks for My Tasks view.
     *
     * @return \Illuminate\Http\Response
     */
    public function myTasks()
    {
        if (Auth::user()->can('manage personal task')) {
            // Get personal tasks
            $personalTasks = PersonalTask::where(function ($query) {
                $query->where('created_by', Auth::user()->id)
                    ->orWhereRaw("find_in_set('" . Auth::user()->id . "',assign_to)");
            })->with(['stage', 'createdBy'])->get();

            // Get project tasks assigned to current user
            $projectTasks = ProjectTask::whereRaw("find_in_set('" . Auth::user()->id . "',assign_to)")
                ->with(['project', 'stage'])
                ->orderBy('created_at', 'desc')
                ->get();

            return view('personal_task.my_tasks', compact('personalTasks', 'projectTasks'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Add a comment to a task.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function addComment(Request $request, $id)
    {
        if (Auth::user()->can('create personal task comment')) {
            $task = PersonalTask::find($id);

            if ($task && ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))) {
                $validator = Validator::make(
                    $request->all(), [
                        'comment' => 'required',
                    ]
                );

                if ($validator->fails()) {
                    return redirect()->back()->with('error', Utility::errorFormat($validator->getMessageBag()));
                }

                $comment = new PersonalTaskComment();
                $comment->task_id = $task->id;
                $comment->user_id = Auth::user()->id;
                $comment->comment = $request->comment;
                $comment->parent_id = $request->parent_id;
                $comment->save();

                return redirect()->back()->with('success', __('Comment added successfully.'));
            } else {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Update task stage (for drag and drop).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStage(Request $request)
    {
        if (Auth::user()->can('edit personal task')) {
            $task = PersonalTask::find($request->task_id);

            if ($task && ($task->created_by == Auth::user()->id || $task->isAssignedTo(Auth::user()->id))) {
                $task->stage_id = $request->stage_id;
                $task->save();

                return response()->json(['success' => true]);
            } else {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        } else {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }
}
