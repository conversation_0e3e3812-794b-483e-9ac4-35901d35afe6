<?php

namespace App\Http\Controllers;

use App\Models\ChartSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChartSettingsController extends Controller
{
    /**
     * Get chart settings for a specific chart
     *
     * @param Request $request
     * @param string $chartId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSettings(Request $request, string $chartId)
    {
        $settings = ChartSetting::where('user_id', Auth::id())
            ->where('chart_id', $chartId)
            ->first();

        return response()->json([
            'success' => true,
            'chart_type' => $settings->chart_type ?? 'bar', // Default to 'bar' if no settings exist
            'settings' => $settings->settings ?? null,
        ]);
    }

    /**
     * Save chart settings for a specific chart
     *
     * @param Request $request
     * @param string $chartId
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveSettings(Request $request, string $chartId)
    {
        $request->validate([
            'chart_type' => 'required|string|in:line,bar,pie,doughnut,radar,table',
            'settings' => 'nullable|array',
        ]);

        $settings = ChartSetting::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'chart_id' => $chartId,
            ],
            [
                'chart_type' => $request->chart_type,
                'settings' => $request->settings ?? [],
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Chart settings saved successfully',
            'settings' => $settings,
        ]);
    }

    /**
     * Reset chart settings to default
     *
     * @param Request $request
     * @param string $chartId
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetSettings(Request $request, string $chartId)
    {
        ChartSetting::where('user_id', Auth::id())
            ->where('chart_id', $chartId)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => 'Chart settings reset to default',
        ]);
    }
}
