<?php

namespace App\Constants;

/**
 * CRM Webhook Action Constants
 * 
 * This class defines all the webhook action types that can be triggered
 * in the CRM system. These actions will be sent to integrated modules
 * via the /saas-webhook endpoint.
 */
class CrmWebhookActions
{
    // Lead Actions
    const LEAD_CREATED = 'crm.lead_created';
    const LEAD_UPDATED = 'crm.lead_updated';
    const LEAD_DELETED = 'crm.lead_deleted';
    const LEAD_STAGE_CHANGED = 'crm.lead_stage_changed';
    const LEAD_CONVERTED_TO_DEAL = 'crm.lead_converted_to_deal';
    const LEAD_ASSIGNED = 'crm.lead_assigned';
    const LEAD_UNASSIGNED = 'crm.lead_unassigned';
    
    // Deal Actions
    const DEAL_CREATED = 'crm.deal_created';
    const DEAL_UPDATED = 'crm.deal_updated';
    const DEAL_DELETED = 'crm.deal_deleted';
    const DEAL_STAGE_CHANGED = 'crm.deal_stage_changed';
    const DEAL_STATUS_CHANGED = 'crm.deal_status_changed';
    const DEAL_ASSIGNED = 'crm.deal_assigned';
    const DEAL_UNASSIGNED = 'crm.deal_unassigned';
    const DEAL_WON = 'crm.deal_won';
    const DEAL_LOST = 'crm.deal_lost';
    const DEAL_DEADLINE_APPROACHING = 'crm.deal_deadline_approaching';
    const DEAL_INACTIVE_FOR_DAYS = 'crm.deal_inactive_for_days';
    const DEAL_NOTES_ADDED = 'crm.deal_notes_added';
    const DEAL_DOCUMENTS_UPLOADED = 'crm.deal_documents_uploaded';
    
    // Task Actions
    const LEAD_TASK_CREATED = 'crm.lead_task_created';
    const LEAD_TASK_UPDATED = 'crm.lead_task_updated';
    const LEAD_TASK_COMPLETED = 'crm.lead_task_completed';
    const LEAD_TASK_DELETED = 'crm.lead_task_deleted';
    const DEAL_TASK_CREATED = 'crm.deal_task_created';
    const DEAL_TASK_UPDATED = 'crm.deal_task_updated';
    const DEAL_TASK_COMPLETED = 'crm.deal_task_completed';
    const DEAL_TASK_DELETED = 'crm.deal_task_deleted';
    
    // Communication Actions
    const LEAD_EMAIL_SENT = 'crm.lead_email_sent';
    const LEAD_CALL_LOGGED = 'crm.lead_call_logged';
    const LEAD_NOTE_ADDED = 'crm.lead_note_added';
    const DEAL_EMAIL_SENT = 'crm.deal_email_sent';
    const DEAL_CALL_LOGGED = 'crm.deal_call_logged';
    const DEAL_NOTE_ADDED = 'crm.deal_note_added';
    
    // File Actions
    const LEAD_FILE_UPLOADED = 'crm.lead_file_uploaded';
    const LEAD_FILE_DELETED = 'crm.lead_file_deleted';
    const DEAL_FILE_UPLOADED = 'crm.deal_file_uploaded';
    const DEAL_FILE_DELETED = 'crm.deal_file_deleted';
    
    // Discussion/Comment Actions
    const LEAD_DISCUSSION_ADDED = 'crm.lead_discussion_added';
    const DEAL_DISCUSSION_ADDED = 'crm.deal_discussion_added';
    
    // Pipeline and Stage Actions
    const PIPELINE_CREATED = 'crm.pipeline_created';
    const PIPELINE_UPDATED = 'crm.pipeline_updated';
    const PIPELINE_DELETED = 'crm.pipeline_deleted';
    const STAGE_CREATED = 'crm.stage_created';
    const STAGE_UPDATED = 'crm.stage_updated';
    const STAGE_DELETED = 'crm.stage_deleted';
    
    // User Actions
    const USER_CREATED = 'crm.user_created';
    const USER_UPDATED = 'crm.user_updated';
    const USER_DELETED = 'crm.user_deleted';
    
    // Label Actions
    const LABEL_CREATED = 'crm.label_created';
    const LABEL_UPDATED = 'crm.label_updated';
    const LABEL_DELETED = 'crm.label_deleted';
    
    // Source Actions
    const SOURCE_CREATED = 'crm.source_created';
    const SOURCE_UPDATED = 'crm.source_updated';
    const SOURCE_DELETED = 'crm.source_deleted';
    
    // Product/Service Actions
    const PRODUCT_CREATED = 'crm.product_created';
    const PRODUCT_UPDATED = 'crm.product_updated';
    const PRODUCT_DELETED = 'crm.product_deleted';

    // Booking & Appointment Actions
    const APPOINTMENT_SCHEDULED = 'booking.appointment_scheduled';
    const APPOINTMENT_RESCHEDULED = 'booking.appointment_rescheduled';
    const APPOINTMENT_CANCELLED = 'booking.appointment_cancelled';
    const APPOINTMENT_REMINDER_TIME_REACHED = 'booking.appointment_reminder_time_reached';
    const EVENT_CREATED = 'booking.event_created';
    const BOOKING_FORM_SUBMITTED = 'booking.booking_form_submitted';
    const BOOKING_CREATED = 'booking.booking_created';
    const APPOINTMENT_BOOKING_CREATED = 'booking.appointment_booking_created';
    const DATE_OVERRIDE_ADDED = 'booking.date_override_added';
    const RECURRING_APPOINTMENT_CREATED = 'booking.recurring_appointment_created';
    const APPOINTMENT_LOCATION_CHANGED = 'booking.appointment_location_changed';
    
    /**
     * Get all available webhook actions
     * 
     * @return array
     */
    public static function getAllActions()
    {
        $reflection = new \ReflectionClass(__CLASS__);
        return array_values($reflection->getConstants());
    }
    
    /**
     * Get actions grouped by category
     * 
     * @return array
     */
    public static function getActionsByCategory()
    {
        return [
            'leads' => [
                self::LEAD_CREATED,
                self::LEAD_UPDATED,
                self::LEAD_DELETED,
                self::LEAD_STAGE_CHANGED,
                self::LEAD_CONVERTED_TO_DEAL,
                self::LEAD_ASSIGNED,
                self::LEAD_UNASSIGNED,
            ],
            'deals' => [
                self::DEAL_CREATED,
                self::DEAL_UPDATED,
                self::DEAL_DELETED,
                self::DEAL_STAGE_CHANGED,
                self::DEAL_STATUS_CHANGED,
                self::DEAL_ASSIGNED,
                self::DEAL_UNASSIGNED,
                self::DEAL_WON,
                self::DEAL_LOST,
                self::DEAL_DEADLINE_APPROACHING,
                self::DEAL_INACTIVE_FOR_DAYS,
                self::DEAL_NOTES_ADDED,
                self::DEAL_DOCUMENTS_UPLOADED,
            ],
            'tasks' => [
                self::LEAD_TASK_CREATED,
                self::LEAD_TASK_UPDATED,
                self::LEAD_TASK_COMPLETED,
                self::LEAD_TASK_DELETED,
                self::DEAL_TASK_CREATED,
                self::DEAL_TASK_UPDATED,
                self::DEAL_TASK_COMPLETED,
                self::DEAL_TASK_DELETED,
            ],
            'communication' => [
                self::LEAD_EMAIL_SENT,
                self::LEAD_CALL_LOGGED,
                self::LEAD_NOTE_ADDED,
                self::DEAL_EMAIL_SENT,
                self::DEAL_CALL_LOGGED,
                self::DEAL_NOTE_ADDED,
            ],
            'files' => [
                self::LEAD_FILE_UPLOADED,
                self::LEAD_FILE_DELETED,
                self::DEAL_FILE_UPLOADED,
                self::DEAL_FILE_DELETED,
            ],
            'discussions' => [
                self::LEAD_DISCUSSION_ADDED,
                self::DEAL_DISCUSSION_ADDED,
            ],
            'system' => [
                self::PIPELINE_CREATED,
                self::PIPELINE_UPDATED,
                self::PIPELINE_DELETED,
                self::STAGE_CREATED,
                self::STAGE_UPDATED,
                self::STAGE_DELETED,
                self::USER_CREATED,
                self::USER_UPDATED,
                self::USER_DELETED,
                self::LABEL_CREATED,
                self::LABEL_UPDATED,
                self::LABEL_DELETED,
                self::SOURCE_CREATED,
                self::SOURCE_UPDATED,
                self::SOURCE_DELETED,
                self::PRODUCT_CREATED,
                self::PRODUCT_UPDATED,
                self::PRODUCT_DELETED,
            ],
            'bookings' => [
                self::APPOINTMENT_SCHEDULED,
                self::APPOINTMENT_RESCHEDULED,
                self::APPOINTMENT_CANCELLED,
                self::APPOINTMENT_REMINDER_TIME_REACHED,
                self::EVENT_CREATED,
                self::BOOKING_FORM_SUBMITTED,
                self::BOOKING_CREATED,
                self::APPOINTMENT_BOOKING_CREATED,
                self::DATE_OVERRIDE_ADDED,
                self::RECURRING_APPOINTMENT_CREATED,
                self::APPOINTMENT_LOCATION_CHANGED,
            ],
        ];
    }
    
    /**
     * Check if an action is valid
     * 
     * @param string $action
     * @return bool
     */
    public static function isValidAction($action)
    {
        return in_array($action, self::getAllActions());
    }
}
