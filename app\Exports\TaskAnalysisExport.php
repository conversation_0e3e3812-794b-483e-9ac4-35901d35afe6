<?php

namespace App\Exports;

use App\Models\ProjectTask;
use App\Models\Project;
use App\Models\User;
use App\Models\TaskStage;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TaskAnalysisExport implements FromCollection, WithHeadings
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Apply the same filters as in the controller
        if ($this->request->filled('search')) {
            $query->where('name', 'like', '%' . $this->request->search . '%');
        }

        if ($this->request->filled('date_range')) {
            $dates = explode(' - ', $this->request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        if ($this->request->filled('client_id')) {
            $query->whereHas('project', function($q) {
                $q->where('client_id', $this->request->client_id);
            });
        }

        if ($this->request->filled('category')) {
            $query->whereHas('project', function($q) {
                $q->where('tags', 'like', '%' . $this->request->category . '%');
            });
        }

        if ($this->request->filled('assigned_by')) {
            $query->where('created_by', $this->request->assigned_by);
        }

        if ($this->request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$this->request->assigned_to]);
        }

        if ($this->request->filled('priority')) {
            $query->where('priority', $this->request->priority);
        }

        if ($this->request->filled('status')) {
            $query->where('stage_id', $this->request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->get();

        $data = collect();

        foreach ($tasks as $task) {
            // Get assigned users
            $assignedUsers = [];
            if (!empty($task->assign_to)) {
                $userIds = explode(',', $task->assign_to);
                $users = User::whereIn('id', $userIds)->get();
                foreach ($users as $user) {
                    $assignedUsers[] = $user->name;
                }
            }

            // Calculate deadline status
            $deadlineStatus = 'On Time';
            if ($task->end_date) {
                $dueDate = Carbon::parse($task->end_date);
                $today = Carbon::today();
                
                if ($dueDate->isPast()) {
                    $deadlineStatus = 'Overdue';
                } elseif ($dueDate->diffInDays($today) <= 3) {
                    $deadlineStatus = 'Due Soon';
                }
            }

            $data->push([
                'task_name' => $task->name,
                'assigned_to' => implode(', ', $assignedUsers),
                'start_date' => $task->start_date,
                'due_date' => $task->end_date,
                'status' => optional($task->stage)->name,
                'deadline_status' => $deadlineStatus,
            ]);
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Task Name',
            'Assigned To',
            'Start Date',
            'Due Date',
            'Status',
            'Deadline Status',
        ];
    }
}
