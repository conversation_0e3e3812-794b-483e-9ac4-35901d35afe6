<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Log;

class SsoTokenAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken() ?? $request->get('token') ?? $request->header('X-SSO-Token');
        
        if (!$token) {
            return response()->json([
                'error' => 'SSO token required',
                'message' => 'Please provide a valid SSO token in Authorization header, token parameter, or X-SSO-Token header'
            ], 401);
        }

        try {
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));
            
            // Check if token is expired
            if ($decoded->exp < time()) {
                return response()->json([
                    'error' => 'Token expired',
                    'message' => 'SSO token has expired. Please generate a new token.'
                ], 401);
            }

            // Check if token audience is correct
            if ($decoded->aud !== 'module-integration') {
                return response()->json([
                    'error' => 'Invalid token audience',
                    'message' => 'SSO token is not valid for this application'
                ], 401);
            }

            $user = User::find($decoded->user_id);
            if (!$user) {
                return response()->json([
                    'error' => 'Invalid user',
                    'message' => 'User associated with SSO token not found'
                ], 401);
            }

            // Set the authenticated user for the request
            Auth::setUser($user);
            
            // Add user info to request for easy access
            $request->merge([
                'sso_user_id' => $user->id,
                'sso_user_email' => $user->email,
                'sso_user_type' => $user->type,
                'sso_token_data' => (array) $decoded
            ]);

            Log::info('SSO Token validated successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'endpoint' => $request->path(),
                'method' => $request->method()
            ]);
            
            return $next($request);
            
        } catch (\Firebase\JWT\ExpiredException $e) {
            return response()->json([
                'error' => 'Token expired',
                'message' => 'SSO token has expired. Please generate a new token.'
            ], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            return response()->json([
                'error' => 'Invalid token signature',
                'message' => 'SSO token signature is invalid'
            ], 401);
        } catch (\Firebase\JWT\BeforeValidException $e) {
            return response()->json([
                'error' => 'Token not yet valid',
                'message' => 'SSO token is not yet valid'
            ], 401);
        } catch (\Exception $e) {
            Log::error('SSO Token validation failed', [
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_trace' => $e->getTraceAsString(),
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'token_preview' => substr($token, 0, 50) . '...'
            ]);

            return response()->json([
                'error' => 'Token validation failed',
                'message' => 'Unable to validate SSO token',
                'debug' => [
                    'error_type' => get_class($e),
                    'error_message' => $e->getMessage()
                ]
            ], 401);
        }
    }
}
