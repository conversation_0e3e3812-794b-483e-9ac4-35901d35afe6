<?php

namespace App\Http\Controllers;

use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Example controller showing how to use booking system webhooks
 * 
 * This controller demonstrates the proper way to integrate webhook
 * dispatching into booking system operations.
 */
class BookingWebhookExampleController extends Controller
{
    /**
     * Example: Schedule an appointment with webhook
     */
    public function scheduleAppointment(Request $request)
    {
        try {
            // Your appointment scheduling logic here
            $appointment = [
                'id' => 123,
                'title' => $request->title,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'client_name' => $request->client_name,
                'client_email' => $request->client_email,
                'location' => $request->location,
                'status' => 'confirmed'
            ];
            
            // Dispatch webhook after successful appointment creation
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchAppointmentScheduled($appointment);
            
            return response()->json([
                'success' => true,
                'message' => 'Appointment scheduled successfully',
                'appointment' => $appointment
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error scheduling appointment: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to schedule appointment'
            ], 500);
        }
    }
    
    /**
     * Example: Reschedule an appointment with webhook
     */
    public function rescheduleAppointment(Request $request, $appointmentId)
    {
        try {
            // Your appointment rescheduling logic here
            $oldDateTime = '2024-01-15T14:00:00.000Z'; // Get from database
            $newDateTime = $request->new_date_time;
            
            $appointment = [
                'id' => $appointmentId,
                'title' => 'Updated Appointment',
                'start_time' => $newDateTime,
                'client_name' => 'John Doe'
            ];
            
            // Dispatch webhook with old and new date times
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchAppointmentRescheduled(
                $appointment, 
                $oldDateTime, 
                $newDateTime
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Appointment rescheduled successfully'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error rescheduling appointment: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to reschedule appointment'
            ], 500);
        }
    }
    
    /**
     * Example: Cancel an appointment with webhook
     */
    public function cancelAppointment(Request $request, $appointmentId)
    {
        try {
            // Your appointment cancellation logic here
            $appointment = [
                'id' => $appointmentId,
                'title' => 'Cancelled Appointment',
                'client_name' => 'Jane Smith'
            ];
            
            $cancellationReason = $request->reason ?? 'No reason provided';
            
            // Dispatch webhook with cancellation reason
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchAppointmentCancelled(
                $appointment, 
                $cancellationReason
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Appointment cancelled successfully'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error cancelling appointment: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel appointment'
            ], 500);
        }
    }
    
    /**
     * Example: Process booking form submission with webhook
     */
    public function processBookingForm(Request $request)
    {
        try {
            // Your booking form processing logic here
            $booking = [
                'id' => 456,
                'client_name' => $request->name,
                'client_email' => $request->email,
                'requested_service' => $request->service,
                'requested_date' => $request->date,
                'status' => 'pending'
            ];
            
            $formData = $request->all();
            
            // Dispatch webhook with form data
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchBookingFormSubmitted($booking, $formData);
            
            return response()->json([
                'success' => true,
                'message' => 'Booking form submitted successfully',
                'booking' => $booking
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error processing booking form: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to process booking form'
            ], 500);
        }
    }
    
    /**
     * Example: Create event with webhook
     */
    public function createEvent(Request $request)
    {
        try {
            // Your event creation logic here
            $event = [
                'id' => 789,
                'title' => $request->title,
                'description' => $request->description,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'location' => $request->location,
                'attendees' => $request->attendees ?? []
            ];
            
            // Dispatch webhook for event creation
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchEventCreated($event);
            
            return response()->json([
                'success' => true,
                'message' => 'Event created successfully',
                'event' => $event
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error creating event: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create event'
            ], 500);
        }
    }
    
    /**
     * Example: Appointment reminder triggered with webhook
     */
    public function triggerAppointmentReminder($appointmentId, $reminderType = 'email')
    {
        try {
            // Your reminder logic here
            $appointment = [
                'id' => $appointmentId,
                'title' => 'Upcoming Appointment',
                'start_time' => now()->addHours(2)->toISOString(),
                'client_name' => 'Bob Johnson',
                'client_email' => '<EMAIL>'
            ];
            
            // Dispatch webhook for reminder
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchAppointmentReminderTimeReached(
                $appointment, 
                $reminderType
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Appointment reminder triggered'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error triggering appointment reminder: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to trigger reminder'
            ], 500);
        }
    }
}
