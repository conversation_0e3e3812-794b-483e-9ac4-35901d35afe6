<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Models\ClientDeal;
use App\Models\Deal;
use App\Models\DealDiscussion;
use App\Models\DealFile;
use App\Models\DealTask;
use App\Models\Pipeline;
use App\Models\UserDeal;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;

class PipelineController extends Controller
{
    use ApiResponser;

    public function __construct()
    {
        $this->middleware(
            [
                'auth',
                'XSS',
            ]
        );
    }

    /**
     * Display a listing of the resource.
     * Automatically filters pipelines based on current authenticated user
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        if(\Auth::user()->can('manage pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            // Filter pipelines by current user's creator (company)
            $pipelines = Pipeline::where('created_by', '=', $currentUser->creatorId())
                ->with(['stages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->with(['leadStages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }])
                ->get();

            // Check if this is an API request
            if ($request->expectsJson() || $request->is('api/*')) {
                $pipelinesData = $pipelines->map(function ($pipeline) {
                    return [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'pipeline_id' => $stage->pipeline_id,
                                'order' => $stage->order,
                                'created_by' => $stage->created_by,
                                'created_at' => $stage->created_at,
                                'updated_at' => $stage->updated_at,
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'pipeline_id' => $leadStage->pipeline_id,
                                'order' => $leadStage->order,
                                'created_by' => $leadStage->created_by,
                                'created_at' => $leadStage->created_at,
                                'updated_at' => $leadStage->updated_at,
                            ];
                        }),
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                    ];
                });

                return $this->success([
                    'pipelines' => $pipelinesData,
                    'total' => $pipelinesData->count(),
                    'user_info' => [
                        'id' => $currentUser->id,
                        'name' => $currentUser->name,
                        'email' => $currentUser->email,
                        'type' => $currentUser->type,
                        'creator_id' => $currentUser->creatorId(),
                        'owner_id' => $currentUser->ownerId()
                    ],
                    'timestamp' => now()->toDateTimeString(),
                ], 'Pipelines retrieved successfully for user: ' . $currentUser->name);
            }

            return view('pipelines.index')->with('pipelines', $pipelines);
        }
        else
        {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error('Permission Denied.', 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if(\Auth::user()->can('create pipeline'))
        {
            return view('pipelines.create');
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Store a newly created resource in storage.
     * Automatically assigns pipeline to current user's company
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        if(\Auth::user()->can('create pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required|max:20',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                if ($request->expectsJson() || $request->is('api/*')) {
                    return $this->error($messages->first(), 422);
                }
                return redirect()->route('pipelines.index')->with('error', $messages->first());
            }

            // Create pipeline for current user's company
            $pipeline             = new Pipeline();
            $pipeline->name       = $request->name;
            $pipeline->created_by = $currentUser->creatorId(); // Assign to current user's company
            $pipeline->save();

            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->success([
                    'pipeline' => [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                    ],
                    'user_info' => [
                        'id' => $currentUser->id,
                        'name' => $currentUser->name,
                        'creator_id' => $currentUser->creatorId(),
                    ],
                    'timestamp' => now()->toDateTimeString(),
                ], 'Pipeline successfully created for user: ' . $currentUser->name);
            }

            return redirect()->route('pipelines.index')->with('success', __('Pipeline successfully created!'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Display the specified resource.
     * Automatically checks if pipeline belongs to current user's company
     *
     * @param \App\Pipeline $pipeline
     * @param Request $request
     *
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function show(Pipeline $pipeline, Request $request)
    {
        if(\Auth::user()->can('manage pipeline'))
        {
            // Get current authenticated user
            $currentUser = \Auth::user();

            // Check if pipeline belongs to current user's company
            if($pipeline->created_by == $currentUser->creatorId())
            {
                // Load relationships filtered by current user
                $pipeline->load(['stages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }]);

                $pipeline->load(['leadStages' => function($query) use ($currentUser) {
                    $query->where('created_by', $currentUser->ownerId())
                          ->orderBy('order');
                }]);

                // Check if this is an API request
                if ($request->expectsJson() || $request->is('api/*')) {
                    $pipelineData = [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'pipeline_id' => $stage->pipeline_id,
                                'order' => $stage->order,
                                'created_by' => $stage->created_by,
                                'created_at' => $stage->created_at,
                                'updated_at' => $stage->updated_at,
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'pipeline_id' => $leadStage->pipeline_id,
                                'order' => $leadStage->order,
                                'created_by' => $leadStage->created_by,
                                'created_at' => $leadStage->created_at,
                                'updated_at' => $leadStage->updated_at,
                            ];
                        }),
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                    ];

                    return $this->success([
                        'pipeline' => $pipelineData,
                        'user_info' => [
                            'id' => $currentUser->id,
                            'name' => $currentUser->name,
                            'email' => $currentUser->email,
                            'creator_id' => $currentUser->creatorId(),
                        ],
                        'timestamp' => now()->toDateTimeString(),
                    ], 'Pipeline retrieved successfully for user: ' . $currentUser->name);
                }
            }
            else
            {
                if ($request->expectsJson() || $request->is('api/*')) {
                    return $this->error('Pipeline not found or access denied for this user.', 403);
                }
            }
        }
        else
        {
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error('Permission Denied.', 403);
            }
        }

        return redirect()->route('pipelines.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(Pipeline $pipeline)
    {
        if(\Auth::user()->can('edit pipeline'))
        {
            if($pipeline->created_by == \Auth::user()->creatorId())
            {
                return view('pipelines.edit', compact('pipeline'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Pipeline $pipeline)
    {
        if(\Auth::user()->can('edit pipeline'))
        {

            if($pipeline->created_by == \Auth::user()->creatorId())
            {

                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required|max:20',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('pipelines.index')->with('error', $messages->first());
                }

                $pipeline->name = $request->name;
                $pipeline->save();

                return redirect()->route('pipelines.index')->with('success', __('Pipeline successfully updated!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Pipeline $pipeline
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Pipeline $pipeline)
    {
        if(\Auth::user()->can('delete pipeline'))
        {
            if($pipeline->created_by == \Auth::user()->creatorId())
            {
                if(count($pipeline->stages) == 0)
                {
                    foreach($pipeline->stages as $stage)
                    {
                        $deals = Deal::where('pipeline_id', '=', $pipeline->id)->where('stage_id', '=', $stage->id)->get();
                        foreach($deals as $deal)
                        {
                            DealDiscussion::where('deal_id', '=', $deal->id)->delete();
                            DealFile::where('deal_id', '=', $deal->id)->delete();
                            ClientDeal::where('deal_id', '=', $deal->id)->delete();
                            UserDeal::where('deal_id', '=', $deal->id)->delete();
                            DealTask::where('deal_id', '=', $deal->id)->delete();
                            ActivityLog::where('deal_id', '=', $deal->id)->delete();

                            $deal->delete();
                        }

                        $stage->delete();
                    }

                    $pipeline->delete();

                    return redirect()->route('pipelines.index')->with('success', __('Pipeline successfully deleted!'));
                }
                else
                {
                    return redirect()->route('pipelines.index')->with('error', __('There are some Stages and Deals on Pipeline, please remove it first!'));
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }
}
