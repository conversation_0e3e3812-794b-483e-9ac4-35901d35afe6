<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Deal;
use App\Models\DealComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DealCommentController extends Controller
{
    public function store(Request $request, $dealId)
    {
        $request->validate([
            'comment' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:deal_comments,id',
            'comment_files.*' => 'nullable|file|max:10240'
        ]);

        $deal = Deal::findOrFail($dealId);
        
        $comment = new DealComment();
        $comment->deal_id = $deal->id;
        $comment->user_id = Auth::id();
        $comment->comment = $request->comment;
        $comment->parent_id = $request->parent_id;
        $comment->created_by = Auth::user()->creatorId();
        $comment->save();

        return redirect()->back()->with('success', __('Comment added successfully.'));
    }

    public function react(Request $request, $commentId)
    {
        $request->validate([
            'reaction' => 'required|in:like,love,laugh,angry,sad,wow'
        ]);

        $comment = DealComment::findOrFail($commentId);
        $username = Auth::user()->name;
        
        $reactions = $comment->comment_reaction ?? [];
        
        $reactions = array_filter($reactions, function($item) use ($username) {
            return $item['username'] !== $username;
        });
        
        $reactions[] = [
            'reaction' => $request->reaction,
            'username' => $username
        ];
        
        $comment->comment_reaction = $reactions;
        $comment->save();

        return response()->json([
            'success' => true,
            'reactions' => $this->getReactionCounts($reactions),
            'userReaction' => $request->reaction
        ]);
    }

    public function destroy($commentId)
    {
        $comment = DealComment::findOrFail($commentId);

        // Check if user can delete this comment
        if ($comment->user_id != Auth::id() && Auth::user()->type != 'super admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $comment->delete();

        return response()->json(['success' => true, 'message' => 'Comment deleted successfully']);
    }

    private function getReactionCounts($reactions)
    {
        $counts = [];
        foreach ($reactions as $item) {
            $reaction = $item['reaction'];
            if (!isset($counts[$reaction])) {
                $counts[$reaction] = 0;
            }
            $counts[$reaction]++;
        }
        return $counts;
    }
}