<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    /**
     * Store a new appointment booking
     */
    public function store(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentPermission('create appointment')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment creation not available in your plan'
            ], 403);
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'contact_name' => 'required|string|max:255',
            'calendar_event_id' => 'required|exists:calendar_events,id',
            'appointment_date' => 'required|date',
            'timeslot' => 'required|string',
            'timezone' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get the calendar event
            $calendarEvent = CalendarEvent::findOrFail($request->calendar_event_id);

            // Check if the event belongs to the current user or is public
            if ($calendarEvent->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to book this event'
                ], 403);
            }

            // Create the booking with time and custom fields
            $booking = Booking::create([
                'event_id' => $request->calendar_event_id,
                'name' => $request->contact_name,
                'email' => null, // Email removed as per user request
                'phone' => null, // Phone removed as per user request
                'date' => $request->appointment_date,
                'time' => $request->timeslot,
                'custom_fields' => $request->has('custom_fields') ? $request->custom_fields : null,
                'custom_fields_value' => $request->has('custom_fields_value') ? $request->custom_fields_value : [
                    'timezone' => $request->timezone
                ]
            ]);

            // Dispatch appointment scheduled webhook
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $appointmentData = [
                    'id' => $booking->id,
                    'title' => $calendarEvent->title ?? 'Appointment',
                    'client_name' => $request->contact_name,
                    'appointment_date' => $request->appointment_date,
                    'timeslot' => $request->timeslot,
                    'timezone' => $request->timezone,
                    'event' => $calendarEvent ? $calendarEvent->toArray() : null,
                    'status' => 'scheduled'
                ];
                $webhookDispatcher->dispatchAppointmentScheduled($appointmentData);
            } catch (\Exception $e) {
                Log::error('Appointment webhook dispatch failed: ' . $e->getMessage());
                // Don't fail the main operation due to webhook issues
            }

            return response()->json([
                'success' => true,
                'message' => 'Appointment booked successfully',
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to book appointment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available appointments/bookings
     */
    public function index(Request $request)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentPermission('view appointment')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment viewing not available in your plan'
            ], 403);
        }

        try {
            $query = Booking::with('event')
                ->whereHas('event', function($q) {
                    $q->where('created_by', Auth::id());
                });

            // Filter by specific event if provided
            if ($request->has('event_id')) {
                $query->where('event_id', $request->event_id);
            }

            // Filter by date range if provided
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('date', [$request->start_date, $request->end_date]);
            }

            // Filter by status if provided
            if ($request->has('status')) {
                $now = Carbon::now();
                if ($request->status === 'upcoming') {
                    $query->where('date', '>=', $now->toDateString());
                } elseif ($request->status === 'past') {
                    $query->where('date', '<', $now->toDateString());
                }
            }

            $appointments = $query->orderBy('date', 'desc')
                                ->orderBy('time', 'desc')
                                ->get();

            return response()->json([
                'success' => true,
                'data' => $appointments
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch appointments: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific appointment
     */
    public function show($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentPermission('show appointment')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment viewing not available in your plan'
            ], 403);
        }

        try {
            $appointment = Booking::with('event')->findOrFail($id);

            // Check if the appointment belongs to the current user's events
            if ($appointment->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view this appointment'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => $appointment
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch appointment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel/delete an appointment
     */
    public function destroy($id)
    {
        // Check permission based on pricing plan
        if (!$this->hasAppointmentPermission('delete appointment')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Appointment deletion not available in your plan'
            ], 403);
        }

        try {
            $appointment = Booking::with('event')->findOrFail($id);

            // Check if the appointment belongs to the current user's events
            if ($appointment->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to cancel this appointment'
                ], 403);
            }

            $appointment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Appointment cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel appointment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if user has appointment permission based on pricing plan
     */
    private function hasAppointmentPermission($permission)
    {
        $user = Auth::user();
        
        // Super admin and system admin always have access
        if ($user->type === 'super admin' || $user->type === 'system admin') {
            return true;
        }

        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            return $user->hasModulePermission('booking', $permission);
        }

        // For other user types, check traditional permissions
        return $user->can($permission);
    }
}
