<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PricingPlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permissions for different user types
        $hasAccess = false;
        if (Auth::user()->type == 'system admin') {
            $hasAccess = true;
        } elseif (Auth::user()->type == 'staff') {
            $hasAccess = Auth::user()->can('view pricing plans');
        }

        if (!$hasAccess) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $query = PricingPlan::query();
        
        // Filter by plan type if provided
        if ($request->filled('plan_type')) {
            $query->where('plan_type', $request->plan_type);
        }
        
        $pricingPlans = $query->orderBy('sort_order')->orderBy('created_at', 'desc')->get();
        
        return view('pricing-plans.index', compact('pricingPlans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $availableModules = PricingPlan::getAvailableModules();
        $statusOptions = PricingPlan::getStatusOptions();
        $durationOptions = PricingPlan::getDurationOptions();
        $planTypeOptions = PricingPlan::getPlanTypeOptions();

        return view('pricing-plans.create', compact('availableModules', 'statusOptions', 'durationOptions', 'planTypeOptions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:pricing_plans,name',
            'plan_type' => 'required|in:subaccount,white_label',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive',
            'duration' => 'required|in:monthly,yearly,lifetime',
            'max_users' => 'required|integer|min:0',
            'max_customers' => 'required|integer|min:0',
            'max_vendors' => 'required|integer|min:0',
            'max_clients' => 'required|integer|min:0',
            'storage_limit' => 'required|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare data for creation
        $data = $request->only([
            'name', 'plan_type', 'description', 'price', 'status', 'duration',
            'max_users', 'max_customers', 'max_vendors', 'max_clients', 'storage_limit'
        ]);
        
        // Handle module permissions
        $modulePermissions = [];
        $availableModules = PricingPlan::getAvailableModules();
        
        foreach ($availableModules as $moduleKey => $moduleData) {
            if ($request->has("modules.{$moduleKey}")) {
                $selectedPermissions = $request->input("permissions.{$moduleKey}", []);
                if (!empty($selectedPermissions)) {
                    $modulePermissions[$moduleKey] = $selectedPermissions;
                }
            }
        }
        
        $data['module_permissions'] = $modulePermissions;
        
        // Handle sort order
        $data['sort_order'] = $request->input('sort_order', 0);

        try {
            PricingPlan::create($data);

            return redirect()->route('pricing-plans.index')
                ->with('success', __('Pricing plan created successfully.'));
        } catch (\Exception $e) {
            \Log::error('Pricing Plan Creation Error: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', __('An error occurred while creating the pricing plan. Please try again.'))
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PricingPlan $pricingPlan)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $availableModules = PricingPlan::getAvailableModules();
        
        return view('pricing-plans.show', compact('pricingPlan', 'availableModules'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PricingPlan $pricingPlan)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $availableModules = PricingPlan::getAvailableModules();
        $statusOptions = PricingPlan::getStatusOptions();
        $durationOptions = PricingPlan::getDurationOptions();
        $planTypeOptions = PricingPlan::getPlanTypeOptions();

        return view('pricing-plans.edit', compact('pricingPlan', 'availableModules', 'statusOptions', 'durationOptions', 'planTypeOptions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PricingPlan $pricingPlan)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:pricing_plans,name,' . $pricingPlan->id,
            'plan_type' => 'required|in:subaccount,white_label',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive',
            'duration' => 'required|in:monthly,yearly,lifetime',
            'max_users' => 'required|integer|min:0',
            'max_customers' => 'required|integer|min:0',
            'max_vendors' => 'required|integer|min:0',
            'max_clients' => 'required|integer|min:0',
            'storage_limit' => 'required|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare data for update
        $data = $request->only([
            'name', 'plan_type', 'description', 'price', 'status', 'duration',
            'max_users', 'max_customers', 'max_vendors', 'max_clients', 'storage_limit'
        ]);
        
        // Handle module permissions
        $modulePermissions = [];
        $availableModules = PricingPlan::getAvailableModules();
        
        foreach ($availableModules as $moduleKey => $moduleData) {
            if ($request->has("modules.{$moduleKey}")) {
                $selectedPermissions = $request->input("permissions.{$moduleKey}", []);
                if (!empty($selectedPermissions)) {
                    $modulePermissions[$moduleKey] = $selectedPermissions;
                }
            }
        }
        
        $data['module_permissions'] = $modulePermissions;
        
        // Handle sort order
        $data['sort_order'] = $request->input('sort_order', 0);

        try {
            $pricingPlan->update($data);

            // Update module_permissions for all users assigned to this pricing plan
            $this->updateUsersModulePermissions($pricingPlan);

            return redirect()->route('pricing-plans.index')
                ->with('success', __('Pricing plan updated successfully.'));
        } catch (\Exception $e) {
            \Log::error('Pricing Plan Update Error: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', __('An error occurred while updating the pricing plan. Please try again.'))
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PricingPlan $pricingPlan)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $pricingPlan->delete();

        return redirect()->route('pricing-plans.index')
            ->with('success', __('Pricing plan deleted successfully.'));
    }

    /**
     * Toggle plan status
     */
    public function toggleStatus(Request $request, PricingPlan $pricingPlan)
    {
        // Check if user is system admin
        if (Auth::user()->type !== 'system admin') {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        $pricingPlan->status = $pricingPlan->status === 'active' ? 'inactive' : 'active';
        $pricingPlan->save();

        return response()->json([
            'success' => true,
            'message' => __('Plan status updated successfully.'),
            'status' => $pricingPlan->status
        ]);
    }

    /**
     * Update module_permissions for all users assigned to this pricing plan
     */
    private function updateUsersModulePermissions(PricingPlan $pricingPlan)
    {
        try {
            // Get all users assigned to this pricing plan
            $users = \App\Models\User::where('plan', $pricingPlan->id)
                ->where('type', 'company')
                ->get();

            foreach ($users as $user) {
                // Update user's module_permissions field
                $user->module_permissions = $pricingPlan->module_permissions ?? [];
                $user->save();

                // Refresh Spatie permissions based on new module permissions
                $user->assignPricingPlanPermissions();

                // Sync to external modules if OMX Flow permissions changed
                if (isset($pricingPlan->module_permissions['omx_flow'])) {
                    $this->syncToOmxFlow($user, $pricingPlan->module_permissions['omx_flow']);
                }

                \Log::info('Updated user module permissions after pricing plan change', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'pricing_plan_id' => $pricingPlan->id,
                    'pricing_plan_name' => $pricingPlan->name,
                    'new_permissions' => $pricingPlan->module_permissions
                ]);
            }

            \Log::info('Successfully updated module permissions for all users in pricing plan', [
                'pricing_plan_id' => $pricingPlan->id,
                'pricing_plan_name' => $pricingPlan->name,
                'users_updated' => $users->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to update users module permissions after pricing plan change', [
                'pricing_plan_id' => $pricingPlan->id,
                'pricing_plan_name' => $pricingPlan->name,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync user to OMX Flow module
     */
    private function syncToOmxFlow($user, $omxFlowPermissions)
    {
        try {
            // Get enabled OMX Flow modules
            $modules = \App\Models\ModuleIntegration::enabled()->get();

            foreach ($modules as $module) {
                // Only sync to modules that match the OMX Flow module
                if ($module->name === 'omx_flow' || $module->name === 'omx-flow' || $module->name === 'OMX FLOW') {
                    $moduleIntegrationController = app(\App\Http\Controllers\ModuleIntegrationController::class);

                    // Prepare additional fields for company creation with default super admin email
                    $additionalFields = [
                        'company_name' => $user->name,
                        'company_description' => 'Company updated via pricing plan integration',
                        'super_admin_email' => '<EMAIL>'
                    ];

                    // Send to external module with permissions and additional data
                    $result = $moduleIntegrationController->syncUserToModule($user, $module, $omxFlowPermissions, $additionalFields);

                    \Log::info('OMX Flow sync result for company after pricing plan update', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'module_name' => $module->name,
                        'permissions' => $omxFlowPermissions,
                        'additional_fields' => $additionalFields,
                        'sync_successful' => $result
                    ]);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to sync company to OMX Flow after pricing plan update', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'permissions' => $omxFlowPermissions,
                'error' => $e->getMessage()
            ]);
        }
    }
}
