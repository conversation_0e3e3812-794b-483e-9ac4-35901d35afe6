<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;

class SystemAdminStaffPermissionController extends Controller
{
    /**
     * Show staff permissions management page
     */
    public function show($staffId)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        // Get the staff member
        $staff = User::where('id', $staffId)
                    ->where('system_admin_company_id', $company->id)
                    ->where('type', 'staff')
                    ->firstOrFail();

        // Get all available permissions grouped by category
        $allPermissions = Permission::all()->groupBy(function($permission) {
            // Group permissions by their prefix (e.g., 'manage user' -> 'user')
            $parts = explode(' ', $permission->name);
            if (count($parts) > 1) {
                return $parts[1]; // Return the second part as category
            }
            return 'general';
        });

        // Get staff's current permissions
        $staffPermissions = $staff->permissions->pluck('name')->toArray();

        return view('system_admin.staff.permissions', compact('staff', 'company', 'allPermissions', 'staffPermissions'));
    }

    /**
     * Update staff permissions
     */
    public function update(Request $request, $staffId)
    {
        if (auth()->user()->type !== 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $company = auth()->user()->systemAdminCompany;
        
        if (!$company) {
            return redirect()->back()->with('error', __('No company found. Please contact administrator.'));
        }

        // Get the staff member
        $staff = User::where('id', $staffId)
                    ->where('system_admin_company_id', $company->id)
                    ->where('type', 'staff')
                    ->firstOrFail();

        // Get selected permissions
        $selectedPermissions = $request->input('permissions', []);

        // Sync permissions (this will remove old permissions and add new ones)
        $staff->syncPermissions($selectedPermissions);

        return redirect()->route('system-admin.staff.permissions', $staffId)
            ->with('success', __('Staff permissions updated successfully.'));
    }

    /**
     * Get system admin sidebar sections that can be granted to staff
     */
    private function getSystemAdminSections()
    {
        return [
            'dashboard' => [
                'name' => 'Dashboard',
                'permissions' => ['view system admin dashboard']
            ],
            'white_label' => [
                'name' => 'White Label Management',
                'permissions' => ['manage user', 'create user', 'edit user', 'delete user']
            ],
            'sub_accounts' => [
                'name' => 'Sub-accounts Management',
                'permissions' => ['manage company', 'create company', 'edit company', 'delete company']
            ],
            'staff_management' => [
                'name' => 'Staff Management',
                'permissions' => ['manage staff', 'create staff', 'edit staff', 'delete staff']
            ],

            'plan_management' => [
                'name' => 'Plan Management',
                'permissions' => ['manage plan', 'create plan', 'edit plan', 'delete plan']
            ],
            'coupon_management' => [
                'name' => 'Coupon Management',
                'permissions' => ['manage coupon', 'create coupon', 'edit coupon', 'delete coupon']
            ],
            'order_management' => [
                'name' => 'Order Management',
                'permissions' => ['manage order']
            ],
            'pricing_plans' => [
                'name' => 'Pricing Plans',
                'permissions' => ['manage pricing plan', 'create pricing plan', 'edit pricing plan', 'delete pricing plan']
            ],
            'support_system' => [
                'name' => 'Support System',
                'permissions' => ['view support system', 'manage support tickets', 'create support tickets', 'edit support tickets', 'delete support tickets']
            ],
            'system_settings' => [
                'name' => 'System Settings',
                'permissions' => ['manage system settings']
            ]
        ];
    }
}
