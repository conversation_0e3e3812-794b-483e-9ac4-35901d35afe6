<?php

namespace App\Http\Controllers;
use App\Models\Milestone;
use App\Models\Projectstages;
use App\Models\TaskStage;
use App\Models\User;
use App\Models\Utility;
use App\Models\ProjectTask;
use App\Models\ProjectStage;
use App\Models\ProjectMilestone;
use App\Models\Timesheet;
use App\Models\Project;
use App\Models\ProjectUser;
use App\Models\UserDefualtView;
use App\Exports\task_reportExport;
use App\Exports\TeamPerformanceExport;
use App\Exports\TaskAnalysisExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;

class ProjectReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
        public function index(Request $request)
    {
        $user = \Auth::user();

        if($user->type == 'client')
        {
            $projects = Project::where('client_id', '=', $user->id);
            $users=[];
            $status=[];

        }
        elseif(\Auth::user()->type == 'company')
        {

            if(isset($request->all_users)&& !empty($request->all_users)){
                $projects = Project::select('projects.*')
                    ->leftjoin('project_users', 'project_users.project_id', 'projects.id')
                    ->where('project_users.user_id', '=', $request->all_users);


            }else{
                $projects = Project::where('projects.created_by', '=', $user->id);
            }

            if(isset($request->status)&& !empty($request->status)){
                $projects->where('status', '=', $request->status);
            }

            if(isset($request->start_date)&& !empty($request->start_date)){
                $projects->whereDate('start_date', '>=', $request->start_date);
            }

            if(isset($request->end_date)&& !empty($request->end_date)){
                $projects->whereDate('end_date', '<=', $request->end_date);
            }

            $users = User::where('created_by', '=', $user->creatorId())->where('type', '!=', 'client')->get();
            $status = Project::$project_status;

        }
        else
        {
            $users =[];
            $projects = Project::select('projects.*')->leftjoin('project_users', 'project_users.project_id', 'projects.id')->where('project_users.user_id', '=', $user->id);
            $status = [];
        }

        $projects = $projects->orderby('id','desc')->with(['tasks'])->get();


        $last_task      = TaskStage::orderBy('order', 'DESC')->where('created_by',\Auth::user()->creatorId())->first();

            return view('project_report.index', compact('projects','users','status', 'last_task'));
        }

        public function show(Request $request,$id)
        {

            $user = \Auth::user();

            if(\Auth::user()->type == 'super admin')
            {
                $users = User::where('created_by', '=', $user->creatorId())->where('type', '=', 'company')->get();
            }
            else
            {
                $users = User::where('created_by', '=', $user->creatorId())->where('type', '!=', 'client')->get();
            }

            if($user->type == 'client')
            {
                $project = Project::where('client_id', '=', $user->id)->where('id',$id)->first();
            }
            elseif(\Auth::user()->type == 'Employee')
            {

                $project = Project::select('projects.*')->leftjoin('project_users', 'project_users.project_id', 'projects.id')->where('project_users.user_id', '=', $user->id)->first();

            }
            else
            {
                $project = Project::where('created_by', '=', $user->id)->where('id',$id)->first();
            }

            if ($user) {
                $chartData = $this->getProjectChart(
                    [
                        'project_id' => $id,
                        'duration' => 'week',
                        ]
                    );
                    $daysleft = round((((strtotime($user->end_date) - strtotime(date('Y-m-d'))) / 24) / 60) / 60);

                    $project_status_task = TaskStage::join("project_tasks", "project_tasks.stage_id", "=", "task_stages.id")
                        ->where('project_tasks.project_id', '=', $id)->groupBy('task_stages.name')
                        ->selectRaw('count(project_tasks.stage_id) as count, task_stages.name as task_stages_name')->pluck('count', 'task_stages_name');

                    $totaltask = ProjectTask::where('project_id',$id)->count();



                    $arrProcessPer_status_task = [];
                    $arrProcess_Label_status_tasks = [];
                    foreach ($project_status_task as $lables => $percentage_stage) {
                         $arrProcess_Label_status_tasks[] = $lables;
                        if ($totaltask == 0) {
                            $arrProcessPer_status_task[] = 0.00;
                        } else {
                            $arrProcessPer_status_task[] = round(($percentage_stage * 100) / $totaltask, 2);
                        }
                    }


                    $project_priority_task = ProjectTask::where('project_id',$id)->groupBy('priority')->selectRaw('count(id) as count, priority')->pluck('count', 'priority');

                    $arrProcessPer_priority = [];
                    $arrProcess_Label_priority = [];
                    foreach ($project_priority_task as $lable => $process) {
                         $arrProcess_Label_priority[] = $lable;
                        if ($totaltask == 0) {
                            $arrProcessPer_priority[] = 0.00;
                        } else {
                            $arrProcessPer_priority[] = round(($process * 100) / $totaltask, 2);
                        }
                    }
                    $arrProcessClass = [
                        'text-success',
                        'text-primary',
                        'text-danger',
                    ];

                      $chartData = app('App\Http\Controllers\ProjectController')->getProjectChart([
                        'created_by' =>$id,
                        'duration' => 'week',
                    ]);

                    $stages = TaskStage::all();
                    $milestones = Milestone::where('project_id' ,$id)->get();
                    $logged_hour_chart = 0;
                    $total_hour = 0;
                    $logged_hour = 0;


                   $tasks = ProjectTask::where('project_id',$id)->get();
                   $data = [];
                   foreach ($tasks as $task)
                   {
                       $timesheets_task = Timesheet::where('task_id',$task->id)->where('project_id',$id)->get();

                    foreach($timesheets_task as $timesheet)
                    {

                        $hours =  date('H', strtotime($timesheet->time));
                        $minutes =  date('i', strtotime($timesheet->time));
                        $total_hour = $hours + ($minutes/60) ;
                        $logged_hour += $total_hour ;
                        $logged_hour_chart = number_format($logged_hour, 2, '.', '');

                   }
               }


                //Estimated Hours
                $esti_logged_hour_chart = ProjectTask::where('project_id',$id)->sum('estimated_hrs');



                $tasks = ProjectTask::where('project_id','=',$id)->get();
                $last_task      = TaskStage::orderBy('order', 'DESC')->where('created_by',\Auth::user()->creatorId())->first();

                return view('project_report.show', compact('user','users', 'arrProcessPer_status_task','arrProcess_Label_priority','esti_logged_hour_chart','logged_hour_chart','arrProcessPer_priority','arrProcess_Label_status_tasks','project','milestones', 'daysleft','chartData','arrProcessClass','stages','tasks','last_task'));

         }
        }



        public function getProjectChart($arrParam)
            {
                $arrDuration = [];
                if ($arrParam['duration'] && $arrParam['duration'] == 'week') {
                    $previous_week = Utility::getFirstSeventhWeekDay(-1);
                    foreach ($previous_week['datePeriod'] as $dateObject) {
                        $arrDuration[$dateObject->format('Y-m-d')] = $dateObject->format('D');
                    }
                }

            $arrTask = [
                'label' => [],
                'color' => [],
            ];


            foreach ($arrDuration as $date => $label) {
                $objProject = ProjectTask::select('stage_id', \DB::raw('count(*) as total'))->whereDate('updated_at', '=', $date)->groupBy('stage_id');

                if (isset($arrParam['project_id'])) {
                    $objProject->where('project_id', '=', $arrParam['project_id']);
                }
                if (isset($arrParam['created_by'])) {
                    $objProject->whereIn(
                        'project_id', function ($query) use ($arrParam) {
                            $query->select('id')->from('projects')->where('created_by', '=', $arrParam['created_by']);
                        }
                    );
                }
                $data = $objProject->pluck('total', 'stage_id')->all();
                $arrTask['label'][] = __($label);

            return $arrTask;
            }
        }

        public function export($id)
        {
            $name = 'task_report_' . date('Y-m-d i:h:s');
            $data = Excel::download(new task_reportExport($id), $name . '.xlsx');
            return $data;
        }

    public function teamPerformance(Request $request)
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Filter: Search Task Name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Filter: Date Range
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        // Filter: Client
        if ($request->filled('client_id')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('client_id', $request->client_id);
            });
        }

        // Filter: Category (assuming category is a tag or custom field)
        if ($request->filled('category')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('tags', 'like', '%' . $request->category . '%');
            });
        }

        // Filter: Assigned By
        if ($request->filled('assigned_by')) {
            $query->where('created_by', $request->assigned_by);
        }

        // Filter: Assigned To
        if ($request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$request->assigned_to]);
        }

        // Filter: Priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter: Status (stage)
        if ($request->filled('status')) {
            $query->where('stage_id', $request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->paginate(20);

        // Filter data for selects
        $clients = Project::where('created_by', $user->creatorId())->with('client')->get()->pluck('client.name', 'client_id')->unique();
        $categories = Project::where('created_by', $user->creatorId())->pluck('tags')->unique()->filter()->values();
        $users = User::where('created_by', $user->creatorId())->where('type', '!=', 'client')->pluck('name', 'id');
        $priorities = ProjectTask::$priority;
        $statuses = TaskStage::where('created_by', $user->creatorId())->pluck('name', 'id');

        return view('projects.team_performance', compact('tasks', 'clients', 'categories', 'users', 'priorities', 'statuses'));
    }

    public function taskAnalysis(Request $request)
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Filter: Search Task Name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Filter: Date Range
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        // Filter: Client
        if ($request->filled('client_id')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('client_id', $request->client_id);
            });
        }

        // Filter: Category (assuming category is a tag or custom field)
        if ($request->filled('category')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('tags', 'like', '%' . $request->category . '%');
            });
        }

        // Filter: Assigned By
        if ($request->filled('assigned_by')) {
            $query->where('created_by', $request->assigned_by);
        }

        // Filter: Assigned To
        if ($request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$request->assigned_to]);
        }

        // Filter: Priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter: Status (stage)
        if ($request->filled('status')) {
            $query->where('stage_id', $request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->paginate(20);

        // Filter data for selects
        $clients = Project::where('created_by', $user->creatorId())->with('client')->get()->pluck('client.name', 'client_id')->unique();
        $categories = Project::where('created_by', $user->creatorId())->pluck('tags')->unique()->filter()->values();
        $users = User::where('created_by', $user->creatorId())->where('type', '!=', 'client')->pluck('name', 'id');
        $priorities = ProjectTask::$priority;
        $statuses = TaskStage::where('created_by', $user->creatorId())->pluck('name', 'id');

        return view('projects.task_analysis', compact('tasks', 'clients', 'categories', 'users', 'priorities', 'statuses'));
    }

    public function performanceAnalysis(Request $request)
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Filters
        if ($request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$request->assigned_to]);
        }
        if ($request->filled('assigned_by')) {
            $query->where('created_by', $request->assigned_by);
        }
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('created_at', '>=', $dates[0]);
                $query->whereDate('created_at', '<=', $dates[1]);
            }
        }
        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        // For filter dropdowns
        $users = User::where('created_by', $user->creatorId())->where('type', '!=', 'client')->pluck('name', 'id');

        // Get selected user for analysis
        $selectedUserId = $request->assigned_to ?? $users->keys()->first();
        $selectedUser = $selectedUserId ? User::find($selectedUserId) : null;

        // Get all tasks for the selected user in the date range
        $tasks = ProjectTask::query();
        $tasks->whereRaw("find_in_set(?, assign_to)", [$selectedUserId]);
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $tasks->whereDate('created_at', '>=', $dates[0]);
                $tasks->whereDate('created_at', '<=', $dates[1]);
            }
        }
        if ($user->type == 'company') {
            $tasks->where('created_by', $user->id);
        }
        $tasks = $tasks->get();

        // Task status counts
        $statusCounts = [
            'allotted' => $tasks->count(),
            'pending' => $tasks->where('stage_id', TaskStage::where('name', 'Pending')->value('id'))->count(),
            'in_progress' => $tasks->where('stage_id', TaskStage::where('name', 'In Progress')->value('id'))->count(),
            'completed' => $tasks->where('stage_id', TaskStage::where('name', 'Completed')->value('id'))->count(),
            'on_hold' => $tasks->where('stage_id', TaskStage::where('name', 'On Hold')->value('id'))->count(),
        ];
        $statusPercents = [];
        foreach ($statusCounts as $key => $val) {
            $statusPercents[$key] = $statusCounts['allotted'] > 0 ? round(($val / $statusCounts['allotted']) * 100, 2) : 0;
        }

        // Weekly/Monthly summary
        $now = now();
        $weekStart = $now->copy()->subDays(6)->startOfDay();
        $monthStart = $now->copy()->subDays(29)->startOfDay();
        $tasksLast7 = $tasks->where('created_at', '>=', $weekStart);
        $tasksLast30 = $tasks->where('created_at', '>=', $monthStart);
        $completedLast7 = $tasksLast7->where('stage_id', TaskStage::where('name', 'Completed')->value('id'))->count();
        $completedLast30 = $tasksLast30->where('stage_id', TaskStage::where('name', 'Completed')->value('id'))->count();

        // Performance metrics
        $completedTasks = $tasks->where('stage_id', TaskStage::where('name', 'Completed')->value('id'));
        $completionRate = $statusCounts['allotted'] > 0 ? round(($statusCounts['completed'] / $statusCounts['allotted']) * 100, 2) : 0;
        // Average completion time
        $totalCompletedTime = 0;
        $completedCount = 0;
        foreach ($completedTasks as $task) {
            if ($task->created_at && $task->end_date) {
                $totalCompletedTime += \Carbon\Carbon::parse($task->end_date)->diffInSeconds($task->created_at);
                $completedCount++;
            }
        }
        $avgCompletion = $completedCount > 0 ? $totalCompletedTime / $completedCount : 0;
        $avgDays = floor($avgCompletion / 86400);
        $avgHours = floor(($avgCompletion % 86400) / 3600);
        $avgMinutes = floor(($avgCompletion % 3600) / 60);
        $avgCompletionStr = $completedCount > 0 ? "$avgDays Days, $avgHours Hours, $avgMinutes Minutes" : '-';
        $avgCompletionRaw = $completedCount > 0 ? $avgCompletion : 0;

        // Calculate performance grade
        $grade = 'E';
        $gradeLabel = 'E (Poor)';
        if ($completionRate >= 95 && $avgCompletion <= 86400) {
            $grade = 'A';
            $gradeLabel = 'A (Excellent)';
        } elseif ($completionRate >= 80 && $avgCompletion <= 2 * 86400) {
            $grade = 'B';
            $gradeLabel = 'B (Good)';
        } elseif ($completionRate >= 60 && $avgCompletion <= 4 * 86400) {
            $grade = 'C';
            $gradeLabel = 'C (Satisfactory)';
        } elseif ($completionRate >= 40 && $avgCompletion <= 7 * 86400) {
            $grade = 'D';
            $gradeLabel = 'D (Needs Improvement)';
        }

        return view('projects.performance_analysis', [
            'users' => $users,
            'selectedUser' => $selectedUser,
            'statusCounts' => $statusCounts,
            'statusPercents' => $statusPercents,
            'tasksAssigned7' => $tasksLast7->count(),
            'tasksCompleted7' => $completedLast7,
            'tasksAssigned30' => $tasksLast30->count(),
            'tasksCompleted30' => $completedLast30,
            'completionRate' => $completionRate,
            'avgCompletionStr' => $avgCompletionStr,
            'avgCompletionRaw' => $avgCompletionRaw,
            'performanceGrade' => $gradeLabel,
            'performanceGradeA' => $grade == 'A' ? $gradeLabel : null,
            'request' => $request,
        ]);
    }

    public function teamPerformanceExport(Request $request)
    {
        $name = 'team_performance_' . date('Y-m-d_H-i-s');
        $data = Excel::download(new TeamPerformanceExport($request), $name . '.xlsx');
        ob_end_clean();

        return $data;
    }

    public function taskAnalysisExport(Request $request)
    {
        $name = 'task_analysis_' . date('Y-m-d_H-i-s');
        $data = Excel::download(new TaskAnalysisExport($request), $name . '.xlsx');
        ob_end_clean();

        return $data;
    }

    public function teamPerformancePdf(Request $request)
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Apply the same filters as in the main method
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        if ($request->filled('client_id')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('client_id', $request->client_id);
            });
        }

        if ($request->filled('category')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('tags', 'like', '%' . $request->category . '%');
            });
        }

        if ($request->filled('assigned_by')) {
            $query->where('created_by', $request->assigned_by);
        }

        if ($request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$request->assigned_to]);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('status')) {
            $query->where('stage_id', $request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->get();
        $priorities = ProjectTask::$priority;

        return view('projects.team_performance_pdf', compact('tasks', 'priorities'));
    }

    public function taskAnalysisPdf(Request $request)
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Apply the same filters as in the main method
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        if ($request->filled('client_id')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('client_id', $request->client_id);
            });
        }

        if ($request->filled('category')) {
            $query->whereHas('project', function($q) use ($request) {
                $q->where('tags', 'like', '%' . $request->category . '%');
            });
        }

        if ($request->filled('assigned_by')) {
            $query->where('created_by', $request->assigned_by);
        }

        if ($request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$request->assigned_to]);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('status')) {
            $query->where('stage_id', $request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->get();

        return view('projects.task_analysis_pdf', compact('tasks'));
    }
}
