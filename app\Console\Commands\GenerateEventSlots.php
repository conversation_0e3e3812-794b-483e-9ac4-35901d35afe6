<?php

namespace App\Console\Commands;

use App\Models\CalendarEvent;
use App\Services\SlotGeneratorService;
use Illuminate\Console\Command;

class GenerateEventSlots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:generate-slots {--event-id= : Generate slots for specific event ID} {--regenerate : Regenerate all slots (delete existing)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate appointment booking slots for calendar events based on their weekly availability';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $slotGenerator = new SlotGeneratorService();
        $eventId = $this->option('event-id');
        $regenerate = $this->option('regenerate');

        if ($eventId) {
            // Generate slots for specific event
            $event = CalendarEvent::find($eventId);
            if (!$event) {
                $this->error("Event with ID {$eventId} not found.");
                return 1;
            }

            $this->info("Generating slots for event: {$event->title} (ID: {$event->id})");

            try {
                if ($regenerate) {
                    $slotsCreated = $slotGenerator->regenerateSlotsForEvent($event);
                    $this->info("Regenerated {$slotsCreated} slots for event ID: {$event->id}");
                } else {
                    $slotsCreated = $slotGenerator->generateSlotsForEvent($event);
                    $this->info("Generated {$slotsCreated} slots for event ID: {$event->id}");
                }
            } catch (\Exception $e) {
                $this->error("Failed to generate slots: " . $e->getMessage());
                return 1;
            }
        } else {
            // Generate slots for all events
            $events = CalendarEvent::all();
            $this->info("Found {$events->count()} events to process");

            $totalSlotsCreated = 0;
            $bar = $this->output->createProgressBar($events->count());

            foreach ($events as $event) {
                try {
                    if ($regenerate) {
                        $slotsCreated = $slotGenerator->regenerateSlotsForEvent($event);
                    } else {
                        $slotsCreated = $slotGenerator->generateSlotsForEvent($event);
                    }
                    $totalSlotsCreated += $slotsCreated;
                } catch (\Exception $e) {
                    $this->error("\nFailed to generate slots for event ID {$event->id}: " . $e->getMessage());
                }
                $bar->advance();
            }

            $bar->finish();
            $this->newLine();
            $this->info("Total slots created: {$totalSlotsCreated}");
        }

        return 0;
    }
}
