<?php

namespace App\Http\Controllers;

use App\Models\ModuleIntegration;
use App\Models\SystemAdminModule;
use App\Models\SuperAdminModulePermission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class ModuleIntegrationController extends Controller
{
    public function index()
    {
        // Only system admin can access module integration management
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $modules = ModuleIntegration::all();
        return view('module-integration.index', compact('modules'));
    }

    public function create()
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('module-integration.create');
    }

    public function store(Request $request)
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'base_url' => 'required|url',
            'sync_endpoint' => 'nullable|string',
            'sso_endpoint' => 'nullable|string',
            'api_token' => 'required|string',
        ]);

        if($validator->fails()) {
            return redirect()->back()->with('error', $validator->errors()->first());
        }

        $module = ModuleIntegration::create([
            'name' => $request->name,
            'base_url' => $request->base_url,
            'sync_endpoint' => $request->sync_endpoint,
            'sso_endpoint' => $request->sso_endpoint,
            'api_token' => $request->api_token,
            'enabled' => $request->has('enabled'),
        ]);

        // Also create corresponding SystemAdminModule entry
        SystemAdminModule::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => 'External module: ' . $request->name,
            'is_active' => $request->has('enabled'),
            'sort_order' => SystemAdminModule::max('sort_order') + 1
        ]);

        // Auto sync users when a module is enabled
        if ($request->has('enabled')) {
            $this->syncAllUsersToModule($module);
        }

        return redirect()->route('module-integration.index')->with('success', __('Module added successfully.'));
    }

    public function edit(ModuleIntegration $moduleIntegration)
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('module-integration.edit', compact('moduleIntegration'));
    }

    public function update(Request $request, ModuleIntegration $moduleIntegration)
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'base_url' => 'required|url',
            'sync_endpoint' => 'nullable|string',
            'sso_endpoint' => 'nullable|string',
            'api_token' => 'required|string',
        ]);

        if($validator->fails()) {
            return redirect()->back()->with('error', $validator->errors()->first());
        }

        $wasEnabled = $moduleIntegration->enabled;

        $moduleIntegration->update([
            'name' => $request->name,
            'base_url' => $request->base_url,
            'sync_endpoint' => $request->sync_endpoint,
            'sso_endpoint' => $request->sso_endpoint,
            'api_token' => $request->api_token,
            'enabled' => $request->has('enabled'),
        ]);

        // Update corresponding SystemAdminModule
        $systemModule = SystemAdminModule::where('name', $moduleIntegration->getOriginal('name'))->first();
        if ($systemModule) {
            $systemModule->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'is_active' => $request->has('enabled'),
            ]);
        }

        // Auto sync users when a module is enabled for the first time
        if (!$wasEnabled && $request->has('enabled')) {
            $this->syncAllUsersToModule($moduleIntegration);
        }

        return redirect()->route('module-integration.index')->with('success', __('Module updated successfully.'));
    }

    public function destroy(ModuleIntegration $moduleIntegration)
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Remove corresponding SystemAdminModule
        $systemModule = SystemAdminModule::where('name', $moduleIntegration->name)->first();
        if ($systemModule) {
            // Remove all permissions first
            SuperAdminModulePermission::where('module_id', $systemModule->id)->delete();
            $systemModule->delete();
        }

        $moduleIntegration->delete();
        return redirect()->route('module-integration.index')->with('success', __('Module deleted successfully.'));
    }

    public function toggleStatus(ModuleIntegration $moduleIntegration)
    {
        if(Auth::user()->type != 'system admin') {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $moduleIntegration->enabled = !$moduleIntegration->enabled;
        $moduleIntegration->save();

        // Update corresponding SystemAdminModule
        $systemModule = SystemAdminModule::where('name', $moduleIntegration->name)->first();
        if ($systemModule) {
            $systemModule->update(['is_active' => $moduleIntegration->enabled]);
        }

        // Auto sync users when enabling
        if ($moduleIntegration->enabled) {
            $this->syncAllUsersToModule($moduleIntegration);
        }

        return response()->json([
            'success' => true,
            'enabled' => $moduleIntegration->enabled,
            'message' => 'Module status updated successfully.'
        ]);
    }

    /**
     * Generate SSO token and redirect to module
     * Now accessible by both system admin and super admin (with permissions)
     */
    public function ssoLogin(ModuleIntegration $moduleIntegration)
    {
        $user = Auth::user();
        
        // System admin has full access
        if ($user->type === 'system admin') {
            return $this->performSSOLogin($moduleIntegration, $user);
        }
        
        // Super admin needs permission for this specific module
        if ($user->type === 'super admin') {
            $systemModule = SystemAdminModule::where('name', $moduleIntegration->name)->first();
            if ($systemModule) {
                $hasPermission = SuperAdminModulePermission::where('super_admin_id', $user->id)
                    ->where('module_id', $systemModule->id)
                    ->where('has_access', true)
                    ->exists();
                    
                if ($hasPermission) {
                    return $this->performSSOLogin($moduleIntegration, $user);
                }
            }
        }

        return redirect()->back()->with('error', __('Permission denied. You do not have access to this module.'));
    }

    /**
     * Perform the actual SSO login
     */
    private function performSSOLogin(ModuleIntegration $moduleIntegration, User $user)
    {
        if (!$moduleIntegration->enabled || !$moduleIntegration->sso_endpoint) {
            return redirect()->back()->with('error', 'Module SSO is not available.');
        }

        $token = $this->generateSSOToken($user);
        
        $ssoUrl = rtrim($moduleIntegration->base_url, '/') . '/' . ltrim($moduleIntegration->sso_endpoint, '/');
        $ssoUrl .= '?token=' . $token;

        return redirect($ssoUrl);
    }

    /**
     * Sync user data to module
     */
    public function syncUserToModule(User $user, ModuleIntegration $module, array $modulePermissions = [], array $additionalFields = [])
    {
        if (!$module->enabled || !$module->sync_endpoint) {
            return false;
        }

        try {
            $syncUrl = rtrim($module->base_url, '/') . '/' . ltrim($module->sync_endpoint, '/');
            
            // Map user type to role for the other project
            $role = $this->mapUserTypeToRole($user->type);
            
            // Get super admin email if user is a company created under a super admin
            $superAdminEmail = null;
            if ($user->type === 'company' && $user->created_by) {
                $superAdmin = User::where('id', $user->created_by)
                    ->where('type', 'super admin')
                    ->first();
                if ($superAdmin) {
                    $superAdminEmail = $superAdmin->email;
                }
            }
            
            // Get company email if user is created inside a company
            $companyEmail = null;
            if ($user->type !== 'company' && $user->type !== 'super admin' && $user->type !== 'system admin' && $user->created_by) {
                $company = User::where('id', $user->created_by)
                    ->where('type', 'company')
                    ->first();
                if ($company) {
                    $companyEmail = $company->email;
                }
            }
            
            // Get module permissions for super admin users
            $userModulePermissions = [];
            if ($user->type === 'super admin') {
                if (!empty($modulePermissions)) {
                    // Use provided module permissions (for new user creation)
                    $userModulePermissions = $modulePermissions;
                } else {
                    // Get existing module permissions from database
                    $permissions = SuperAdminModulePermission::where('super_admin_id', $user->id)
                        ->with('module')
                        ->get();
                    
                    foreach ($permissions as $permission) {
                        if ($permission->has_access && $permission->module) {
                            $userModulePermissions[] = $permission->module->name;
                        }
                    }
                }
            } elseif ($user->type === 'company') {
                // For company users, get module permissions from parameter or user attribute
                if (!empty($modulePermissions)) {
                    $userModulePermissions = $modulePermissions;
                } elseif (!empty($user->module_permissions)) {
                    $userModulePermissions = $user->module_permissions;
                }
            }
            
            // Prepare data in the format expected by the other project
            $userData = [
                'email' => $user->email,
                'name' => $user->name,
                'role' => $role,
                'first_name' => $this->extractFirstName($user->name),
                'last_name' => $this->extractLastName($user->name),
                'status' => 1, // Default to active
                'password' => $user->password, // Pass hashed password
            ];

            // Add additional fields if provided
            if (!empty($additionalFields['company_name'])) {
                $userData['company_name'] = $additionalFields['company_name'];
            }
            if (!empty($additionalFields['company_description'])) {
                $userData['company_description'] = $additionalFields['company_description'];
            }
            if (!empty($additionalFields['super_admin_email'])) {
                $userData['super_admin_email'] = $additionalFields['super_admin_email'];
            }

            // Set company_id only if additional fields are not provided
            if (empty($additionalFields['company_name']) && empty($additionalFields['company_description'])) {
                $userData['company_id'] = $user->created_by ?? 1; // Use created_by as company_id, fallback to 1
            }

            // Add super admin email if available (fallback to parameter)
            if ($superAdminEmail && empty($userData['super_admin_email'])) {
                $userData['super_admin_email'] = $superAdminEmail;
            }

            // Add company email if available
            if ($companyEmail) {
                $userData['company_email'] = $companyEmail;
            }

            // Add module permissions for super admin users
            if (($user->type === 'super admin' || $user->type === 'company') && !empty($userModulePermissions)) {
                $userData['module_permissions'] = $userModulePermissions;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $module->api_token,
                'Content-Type' => 'application/json',
            ])->post($syncUrl, $userData);

            // Log sync response
            Log::info('User sync to module', [
                'module' => $module->name,
                'user_id' => $user->id,
                'user_type' => $user->type,
                'module_permissions' => $userModulePermissions,
                'additional_fields' => $additionalFields,
                'super_admin_email' => $superAdminEmail,
                'company_email' => $companyEmail,
                'status' => $response->status(),
                'response' => $response->body(),
                'sent_data' => $userData
            ]);

            return $response->successful();

        } catch (\Exception $e) {
            // Log sync failure
            Log::error('User sync failed', [
                'module' => $module->name,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Map user type to role for the other project
     */
    private function mapUserTypeToRole($userType)
    {
        $roleMapping = [
            'system admin' => 'system_admin',
            'super admin' => 'super_admin', 
            'company' => 'company_admin',
            'employee' => 'employee',
            'client' => 'client',
            'manager' => 'manager',
            'hr' => 'hr',
        ];

        return $roleMapping[$userType] ?? 'employee';
    }

    /**
     * Extract first name from full name
     */
    private function extractFirstName($fullName)
    {
        $nameParts = explode(' ', trim($fullName));
        return $nameParts[0] ?? '';
    }

    /**
     * Extract last name from full name
     */
    private function extractLastName($fullName)
    {
        $nameParts = explode(' ', trim($fullName));
        if (count($nameParts) > 1) {
            array_shift($nameParts); // Remove first name
            return implode(' ', $nameParts);
        }
        return '';
    }

    /**
     * Sync all users to a module
     */
    private function syncAllUsersToModule(ModuleIntegration $module)
    {
        $users = User::all();
        $syncCount = 0;

        foreach ($users as $user) {
            if ($this->syncUserToModule($user, $module)) {
                $syncCount++;
            }
        }

        Log::info('Bulk user sync completed', [
            'module' => $module->name,
            'total_users' => $users->count(),
            'synced_users' => $syncCount
        ]);

        return $syncCount;
    }

    /**
     * Generate SSO token for user
     */
    private function generateSSOToken(User $user)
    {
        $payload = [
            'iss' => config('app.url'), // Issuer
            'aud' => 'module-integration', // Audience
            'iat' => time(), // Issued at
            'exp' => time() + (60 * 60 * 24 * 365 * 10), // Expires in 10 years (lifetime)
            'user_id' => $user->id,
            'email' => $user->email,
            'name' => $user->name,
            'type' => $user->type,
        ];

        return JWT::encode($payload, env('SSO_SECRET', 'default-secret'), 'HS256');
    }

    /**
     * API endpoint to receive sync requests from modules
     */
    public function apiSyncUser(Request $request)
    {
        // Verify API token
        $authHeader = $request->header('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $token = substr($authHeader, 7);
        $module = ModuleIntegration::where('api_token', $token)->first();

        if (!$module || !$module->enabled) {
            return response()->json(['error' => 'Invalid or disabled module'], 401);
        }

        $request->validate([
            'id' => 'required|integer',
            'name' => 'required|string',
            'email' => 'required|email',
            'type' => 'required|string',
        ]);

        try {
            $user = User::updateOrCreate(
                ['id' => $request->id],
                [
                    'name' => $request->name,
                    'email' => $request->email,
                    'type' => $request->type,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'User synced successfully',
                'user_id' => $user->id
            ]);

        } catch (\Exception $e) {
            Log::error('API user sync failed', [
                'module' => $module->name,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Sync failed'], 500);
        }
    }

    /**
     * API endpoint to handle user updates from modules
     */
    public function apiUpdateUser(Request $request)
    {
        // Verify API token
        $authHeader = $request->header('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $token = substr($authHeader, 7);
        $module = ModuleIntegration::where('api_token', $token)->first();

        if (!$module || !$module->enabled) {
            return response()->json(['error' => 'Invalid or disabled module'], 401);
        }

        $request->validate([
            'email' => 'required|email',
            'name' => 'sometimes|string',
            'type' => 'sometimes|string',
        ]);

        try {
            $user = User::where('email', $request->email)->first();
            
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Update user with provided data
            $updateData = [];
            if ($request->has('name')) {
                $updateData['name'] = $request->name;
            }
            if ($request->has('type')) {
                $updateData['type'] = $request->type;
            }

            if (!empty($updateData)) {
                $user->update($updateData);
            }

            Log::info('API user update successful', [
                'module' => $module->name,
                'user_id' => $user->id,
                'user_email' => $user->email,
                'updated_fields' => array_keys($updateData)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User updated successfully',
                'user_id' => $user->id
            ]);

        } catch (\Exception $e) {
            Log::error('API user update failed', [
                'module' => $module->name,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Update failed'], 500);
        }
    }

    /**
     * API endpoint to handle user deletions from modules
     */
    public function apiDeleteUser(Request $request)
    {
        // Verify API token
        $authHeader = $request->header('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $token = substr($authHeader, 7);
        $module = ModuleIntegration::where('api_token', $token)->first();

        if (!$module || !$module->enabled) {
            return response()->json(['error' => 'Invalid or disabled module'], 401);
        }

        $request->validate([
            'email' => 'required|email',
        ]);

        try {
            $user = User::where('email', $request->email)->first();
            
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Store user info for logging before deletion
            $userInfo = [
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
                'type' => $user->type
            ];

            // Delete the user
            $user->delete();

            Log::info('API user deletion successful', [
                'module' => $module->name,
                'deleted_user' => $userInfo
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('API user deletion failed', [
                'module' => $module->name,
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Deletion failed'], 500);
        }
    }

    /**
     * Sync user to Automatish using external-signup endpoint
     */
    public function syncUserToAutomatish(User $user, $plainPassword = null)
    {
        // Find Automatish module integration
        $automatishModule = ModuleIntegration::where('name', 'Automatish')
            ->where('enabled', true)
            ->first();

        if (!$automatishModule || !$automatishModule->sync_endpoint) {
            Log::warning('Automatish module not found or not enabled', [
                'user_id' => $user->id,
                'module_found' => $automatishModule ? true : false,
                'sync_endpoint' => $automatishModule ? $automatishModule->sync_endpoint : null
            ]);
            return false;
        }

        try {
            $syncUrl = rtrim($automatishModule->base_url, '/') . '/' . ltrim($automatishModule->sync_endpoint, '/');

            // Prepare data in the format expected by Automatish external-signup endpoint
            $userData = [
                'fullName' => $user->name,
                'email' => $user->email,
                'password' => $plainPassword ?: 'defaultPassword123' // Use plain password if available
            ];

            // Log the request details before sending
            Log::info('Sending request to Automatish', [
                'user_id' => $user->id,
                'sync_url' => $syncUrl,
                'payload' => $userData,
                'headers' => [
                    'Authorization' => 'Bearer ' . substr($automatishModule->api_token, 0, 10) . '...',
                    'Content-Type' => 'application/json'
                ],
                'module_config' => [
                    'name' => $automatishModule->name,
                    'base_url' => $automatishModule->base_url,
                    'sync_endpoint' => $automatishModule->sync_endpoint,
                    'enabled' => $automatishModule->enabled
                ]
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $automatishModule->api_token,
                'Content-Type' => 'application/json',
            ])->post($syncUrl, $userData);

            // Log detailed response
            Log::info('Automatish response received', [
                'user_id' => $user->id,
                'email' => $user->email,
                'response_status' => $response->status(),
                'response_headers' => $response->headers(),
                'response_body' => $response->body(),
                'response_json' => $response->json(),
                'request_duration' => 'N/A' // HTTP client doesn't provide this easily
            ]);

            if ($response->successful()) {
                Log::info('User successfully synced to Automatish', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'status_code' => $response->status()
                ]);
                return true;
            } else {
                Log::error('Failed to sync user to Automatish', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                    'sent_payload' => $userData
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('Exception during Automatish sync', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'sent_payload' => isset($userData) ? $userData : null,
                'sync_url' => isset($syncUrl) ? $syncUrl : null
            ]);
            return false;
        }
    }

    /**
     * Handle SSO login from modules
     */
    public function handleSSOLogin(Request $request)
    {
        $token = $request->get('token');

        if (!$token) {
            return redirect()->route('login')->with('error', 'Invalid SSO token');
        }

        try {
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));

            $user = User::where('email', $decoded->email)->first();

            if (!$user) {
                return redirect()->route('login')->with('error', 'User not found');
            }

            Auth::login($user);

            return redirect()->route('dashboard')->with('success', 'SSO login successful');

        } catch (\Exception $e) {
            Log::error('SSO login failed', ['error' => $e->getMessage()]);
            return redirect()->route('login')->with('error', 'Invalid or expired SSO token');
        }
    }
}
