<?php

namespace App\Exports;

use App\Models\ProjectTask;
use App\Models\Project;
use App\Models\User;
use App\Models\TaskStage;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Http\Request;

class TeamPerformanceExport implements FromCollection, WithHeadings
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $user = \Auth::user();
        $query = ProjectTask::query();

        // Apply the same filters as in the controller
        if ($this->request->filled('search')) {
            $query->where('name', 'like', '%' . $this->request->search . '%');
        }

        if ($this->request->filled('date_range')) {
            $dates = explode(' - ', $this->request->date_range);
            if (count($dates) === 2) {
                $query->whereDate('start_date', '>=', $dates[0]);
                $query->whereDate('end_date', '<=', $dates[1]);
            }
        }

        if ($this->request->filled('client_id')) {
            $query->whereHas('project', function($q) {
                $q->where('client_id', $this->request->client_id);
            });
        }

        if ($this->request->filled('category')) {
            $query->whereHas('project', function($q) {
                $q->where('tags', 'like', '%' . $this->request->category . '%');
            });
        }

        if ($this->request->filled('assigned_by')) {
            $query->where('created_by', $this->request->assigned_by);
        }

        if ($this->request->filled('assigned_to')) {
            $query->whereRaw("find_in_set(?, assign_to)", [$this->request->assigned_to]);
        }

        if ($this->request->filled('priority')) {
            $query->where('priority', $this->request->priority);
        }

        if ($this->request->filled('status')) {
            $query->where('stage_id', $this->request->status);
        }

        // Only show tasks user is allowed to see
        if ($user->type == 'company') {
            $query->where('created_by', $user->id);
        } elseif ($user->type == 'employee') {
            $query->whereRaw("find_in_set(?, assign_to)", [$user->id]);
        }

        $tasks = $query->with(['project.client', 'stage'])->orderBy('id', 'desc')->get();

        $data = collect();
        $priorities = ProjectTask::$priority;

        foreach ($tasks as $task) {
            // Get assigned users
            $assignedUsers = [];
            if (!empty($task->assign_to)) {
                $userIds = explode(',', $task->assign_to);
                $users = User::whereIn('id', $userIds)->get();
                foreach ($users as $user) {
                    $assignedUsers[] = $user->name;
                }
            }

            $data->push([
                'task_name' => $task->name,
                'assigned_to' => implode(', ', $assignedUsers),
                'assigned_by' => optional($task->createdBy)->name,
                'client_name' => optional($task->project->client)->name,
                'priority' => $priorities[$task->priority] ?? $task->priority,
                'status' => optional($task->stage)->name,
                'start_date' => $task->start_date,
                'due_date' => $task->end_date,
            ]);
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'Task Name',
            'Assigned To',
            'Assigned By',
            'Client Name',
            'Priority',
            'Status',
            'Start Date',
            'Due Date',
        ];
    }
}
