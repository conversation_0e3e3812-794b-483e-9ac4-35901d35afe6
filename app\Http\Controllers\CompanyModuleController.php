<?php

namespace App\Http\Controllers;

use App\Models\ModuleIntegration;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;

class CompanyModuleController extends Controller
{
    /**
     * Display available modules for companies
     */
    public function index()
    {
        // Allow company users and super admins
        if(!in_array(Auth::user()->type, ['company', 'super admin'])) {
            return redirect()->back()->with('error', __('Access denied. This page is for company users and super admins.'));
        }

        // Get only enabled modules
        $modules = ModuleIntegration::enabled()->get();
        
        return view('company.modules.index', compact('modules'));
    }

    /**
     * Launch the first available module directly (OMX Flow)
     */
    public function launchOmxFlow()
    {
        $user = Auth::user();

        // Allow company users and super admins
        if(!in_array($user->type, ['company', 'super admin'])) {
            return redirect()->back()->with('error', __('Access denied. This page is for company users and super admins.'));
        }

        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            if (!$user->hasModulePermission('omx_flow', 'access omx flow')) {
                return redirect()->back()->with('error', __('Access denied. Your pricing plan does not include OMX Flow access.'));
            }
        }

        // Get the first enabled module with SSO endpoint
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('No OMX Flow module is available. Please contact your administrator.'));
        }

        // Launch the module directly
        return $this->ssoLogin($module);
    }

    /**
     * Launch Automatish module directly
     */
    public function launchAutomatish()
    {
        $user = Auth::user();

        // Allow company users and super admins
        if(!in_array($user->type, ['company', 'super admin'])) {
            return redirect()->back()->with('error', __('Access denied. This page is for company users and super admins.'));
        }

        // For company users, check pricing plan permissions
        if ($user->type === 'company') {
            if (!$user->hasModulePermission('automatish', 'access automatish')) {
                return redirect()->back()->with('error', __('Access denied. Your pricing plan does not include Automatish access.'));
            }
        }

        // Get the Automatish module
        $module = ModuleIntegration::enabled()
            ->where('name', 'Automatish')
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Automatish module is not available. Please contact your administrator.'));
        }

        // Launch the module directly
        return $this->ssoLogin($module);
    }

    /**
     * Launch WhatsApp Template module directly
     */
    public function launchWhatsAppTemplate()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for WhatsApp features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp Template module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp template URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappTemplateUrl = $baseUrl . '/vendor-console/whatsapp/templates?sso_token=' . $token;

        return redirect($whatsappTemplateUrl);
    }

    /**
     * Launch WhatsApp Flow module directly
     */
    public function launchWhatsAppFlow()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for WhatsApp features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp Flow module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp flow URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappFlowUrl = $baseUrl . '/vendor-console/whatsapp/flows?sso_token=' . $token;

        return redirect($whatsappFlowUrl);
    }

    /**
     * Launch WhatsApp Campaign module directly
     */
    public function launchWhatsAppCampaign()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for WhatsApp features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp Campaign module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp campaign URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappCampaignUrl = $baseUrl . '/vendor-console/whatsapp/campaign?sso_token=' . $token;

        return redirect($whatsappCampaignUrl);
    }

    /**
     * Launch WhatsApp Orders module directly
     */
    public function launchWhatsAppOrders()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for WhatsApp features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp Orders module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp orders URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappOrdersUrl = $baseUrl . '/vendor-console/whatsapp-orders?sso_token=' . $token;

        return redirect($whatsappOrdersUrl);
    }

    /**
     * Launch WhatsApp Chat module directly
     */
    public function launchWhatsAppChat()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for WhatsApp features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp Chat module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp chat URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappChatUrl = $baseUrl . '/vendor-console/whatsapp/contact/chat?sso_token=' . $token;

        return redirect($whatsappChatUrl);
    }

    /**
     * Launch Facebook Chat module directly
     */
    public function launchFacebookChat()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for social media features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Facebook Chat module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the Facebook chat URL
        $baseUrl = rtrim($module->base_url, '/');
        $facebookChatUrl = $baseUrl . '/vendor-console/facebook/chat?sso_token=' . $token;

        return redirect($facebookChatUrl);
    }

    /**
     * Launch Instagram Chat module directly
     */
    public function launchInstagramChat()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for social media features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Instagram Chat module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the Instagram chat URL
        $baseUrl = rtrim($module->base_url, '/');
        $instagramChatUrl = $baseUrl . '/vendor-console/instagram/chat?sso_token=' . $token;

        return redirect($instagramChatUrl);
    }

    /**
     * Launch WhatsApp API Setup
     */
    public function launchWhatsAppApiSetup()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('WhatsApp API Setup module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the WhatsApp API setup URL
        $baseUrl = rtrim($module->base_url, '/');
        $whatsappApiSetupUrl = $baseUrl . '/vendor-console/settings/whatsapp-cloud-api-setup?sso_token=' . $token;

        return redirect($whatsappApiSetupUrl);
    }

    /**
     * Launch Facebook API Setup
     */
    public function launchFacebookApiSetup()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Facebook API Setup module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the Facebook API setup URL
        $baseUrl = rtrim($module->base_url, '/');
        $facebookApiSetupUrl = $baseUrl . '/vendor-console/settings/facebook-api-setup?sso_token=' . $token;

        return redirect($facebookApiSetupUrl);
    }

    /**
     * Launch Instagram API Setup
     */
    public function launchInstagramApiSetup()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Instagram API Setup module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the Instagram API setup URL
        $baseUrl = rtrim($module->base_url, '/');
        $instagramApiSetupUrl = $baseUrl . '/vendor-console/settings/instagram-api-setup?sso_token=' . $token;

        return redirect($instagramApiSetupUrl);
    }

    /**
     * Launch Bot Replies module directly
     */
    public function launchBotReplies()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for bot features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Bot Replies module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the bot replies URL
        $baseUrl = rtrim($module->base_url, '/');
        $botRepliesUrl = $baseUrl . '/vendor-console/bot-replies?sso_token=' . $token;

        return redirect($botRepliesUrl);
    }

    /**
     * Launch Bot Flows module directly
     */
    public function launchBotFlows()
    {
        $user = Auth::user();

        // Allow all users (no restrictions as requested)
        if(!in_array($user->type, ['company', 'super admin', 'employee', 'client', 'staff'])) {
            return redirect()->back()->with('error', __('Access denied.'));
        }

        // Get the OMX Flow module (assuming it's the base for bot features)
        $module = ModuleIntegration::enabled()
            ->whereNotNull('sso_endpoint')
            ->where('sso_endpoint', '!=', '')
            ->first();

        if (!$module) {
            return redirect()->back()->with('error', __('Bot Flows module is not available. Please contact your administrator.'));
        }

        // Generate SSO token
        $token = $this->generateSSOToken($user);

        // Construct the bot flows URL
        $baseUrl = rtrim($module->base_url, '/');
        $botFlowsUrl = $baseUrl . '/vendor-console/bot-replies/bot-flows?sso_token=' . $token;

        return redirect($botFlowsUrl);
    }

    /**
     * Handle SSO login for company users
     */
    public function ssoLogin(ModuleIntegration $moduleIntegration)
    {
        // Allow company users and super admins
        if(!in_array(Auth::user()->type, ['company', 'super admin'])) {
            return redirect()->back()->with('error', __('Access denied. This page is for company users and super admins.'));
        }

        if (!$moduleIntegration->enabled || !$moduleIntegration->sso_endpoint) {
            return redirect()->back()->with('error', 'Module SSO is not available.');
        }

        $user = Auth::user();
        $token = $this->generateSSOToken($user);

        $ssoUrl = rtrim($moduleIntegration->base_url, '/') . '/' . ltrim($moduleIntegration->sso_endpoint, '/');
        $ssoUrl .= '?token=' . $token;

        return redirect($ssoUrl);
    }

    /**
     * Generate SSO token for user
     */
    private function generateSSOToken(User $user)
    {
        $payload = [
            'iss' => config('app.url'), // Issuer
            'aud' => 'module-integration', // Audience
            'iat' => time(), // Issued at
            'exp' => time() + (60 * 60 * 24 * 365 * 10), // Expires in 10 years (lifetime)
            'user_id' => $user->id,
            'email' => $user->email,
            'name' => $user->name,
            'type' => $user->type,
        ];

        return JWT::encode($payload, env('SSO_SECRET', 'default-secret'), 'HS256');
    }
}
