<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ProjectUser;
use App\Models\User;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\AssignProject;
use App\Models\Project;
use App\Models\Utility;
use App\Models\Tag;
use App\Models\ProjectTask;
use App\Models\TimeTracker;
use App\Models\TrackPhoto;
use App\Models\Pipeline;
use App\Models\Stage;
use App\Models\LeadStage;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use App\Models\Lead;
use App\Models\Deal;


class ApiController extends Controller
{
    //
    use ApiResponser;

    public function login(Request $request)
    {

        $attr = $request->validate([
            'email' => 'required|string|email|',
            'password' => 'required|string'
        ]);

        if (!Auth::attempt($attr)) {
            return $this->error('Credentials not match', 401);
        }

        $settings              = Utility::settings(auth()->user()->id);

        $settings = [
            'shot_time'=> isset($settings['interval_time'])?$settings['interval_time']:0.5,
        ];

        return $this->success([
            'token' => auth()->user()->createToken('API Token')->plainTextToken,
            'user'=> auth()->user()->id,
            'settings' =>$settings,
        ],'Login successfully.');
    }
    public function logout()
    {
        auth()->user()->tokens()->delete();
        return $this->success([],'Tokens Revoked');
    }


    public function getProjects(Request $request)
    {

        $user = auth()->user();

        if($user->type!='company')
        {
            $assign_pro_ids = ProjectUser::where('user_id',$user->id)->pluck('project_id');


            $project_s      = Project::with('tasks')->whereIn('id', $assign_pro_ids)->get()->toArray();

        }
        else
        {


            $project_s = Project::with('tasks')->where('created_by', $user->id)->get()->toArray();


        }

        return $this->success([
            'projects' => $project_s,
        ],'Get Project List successfully.');
    }


    public function addTracker(Request $request){

        $user = auth()->user();
        if($request->has('action') && $request->action == 'start'){

            $validatorArray = [
                'task_id' => 'required|integer',
            ];
            $validator      = \Validator::make(
                $request->all(), $validatorArray
            );
            if($validator->fails())
            {
                return $this->error($validator->errors()->first(), 401);
            }
            $task= ProjectTask::find($request->task_id);

            if(empty($task)){
                return $this->error('Invalid task', 401);
            }

            $project_id = isset($task->project_id)?$task->project_id:'';
            TimeTracker::where('created_by', '=', $user->id)->where('is_active', '=', 1)->update(['end_time' => date("Y-m-d H:i:s")]);

            $track['name']        = $request->has('workin_on') ? $request->input('workin_on') : '';
            $track['project_id']  = $project_id;
            $track['is_billable'] =  $request->has('is_billable')? $request->is_billable:0;
            $track['tag_id']      = $request->has('workin_on') ? $request->input('workin_on') : '';
            $track['start_time']  = $request->has('time') ?  date("Y-m-d H:i:s",strtotime($request->input('time'))) : date("Y-m-d H:i:s");
            $track['task_id']     = $request->has('task_id') ? $request->input('task_id') : '';
            $track['user_id']     = $user->id;
            $track['created_by']  = $user->creatorId();
            $track                = TimeTracker::create($track);
            $track->action        ='start';

            return $this->success( $track,'Track successfully create.');
        }else{
            $validatorArray = [
                'task_id' => 'required|integer',
                'traker_id' =>'required|integer',
            ];
            $validator      = Validator::make(
                $request->all(), $validatorArray
            );
            if($validator->fails())
            {
                return Utility::error_res($validator->errors()->first());
            }
            $tracker = TimeTracker::where('id',$request->traker_id)->first();
            if($tracker)
            {
                $tracker->end_time   = $request->has('time') ?  date("Y-m-d H:i:s",strtotime($request->input('time'))) : date("Y-m-d H:i:s");
                $tracker->is_active  = 0;
                $tracker->total_time = Utility::diffance_to_time($tracker->start_time, $tracker->end_time);
                $tracker->save();
                return $this->success( $tracker,'Stop time successfully.');
            }
        }

    }
    public function uploadImage(Request $request){
        $user = auth()->user();
        $image_base64 = base64_decode($request->img);
        $file =$request->imgName;
        if($request->has('tracker_id') && !empty($request->tracker_id)){
            $app_path = storage_path('uploads/traker_images/').$request->tracker_id.'/';
            if (!file_exists($app_path)) {
                mkdir($app_path, 0777, true);
            }

        }else{
            $app_path = storage_path('uploads/traker_images/');
            if (!is_dir($app_path)) {
                mkdir($app_path, 0777, true);
            }
        }
        $file_name =  $app_path.$file;
        file_put_contents( $file_name, $image_base64);
        $new = new TrackPhoto();
        $new->track_id = $request->tracker_id;
        $new->user_id  = $user->id;
        $new->img_path  = 'uploads/traker_images/'.$request->tracker_id.'/'.$file;
        $new->time  = $request->time;
        $new->status  = 1;
        $new->created_by  = $user->creatorId();
        $new->save();
        return $this->success( [],'Uploaded successfully.');
    }

    public function getSsoContacts(Request $request)
    {
        // Get user if authenticated, otherwise use null
        $user = auth()->user();
        $userId = $user ? $user->id : 'anonymous';
        
        // Log the API call
        Log::info('SSO Contacts API called', [
            'user_id' => $userId,
            'timestamp' => now(),
            'request_data' => $request->all(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // Sample contacts data for now
        $contacts = [
            [
                'id' => 1,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'company' => 'Example Corp',
                'created_at' => now()->toDateTimeString()
            ],
            [
                'id' => 2,
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '+1234567891',
                'company' => 'Sample Inc',
                'created_at' => now()->toDateTimeString()
            ],
            [
                'id' => 3,
                'name' => 'Bob Johnson',
                'email' => '<EMAIL>',
                'phone' => '+1234567892',
                'company' => 'Test LLC',
                'created_at' => now()->toDateTimeString()
            ],
            [
                'id' => 4,
                'name' => 'Alice Wilson',
                'email' => '<EMAIL>',
                'phone' => '+1234567893',
                'company' => 'Demo Ltd',
                'created_at' => now()->toDateTimeString()
            ],
            [
                'id' => 5,
                'name' => 'Charlie Brown',
                'email' => '<EMAIL>',
                'phone' => '+1234567894',
                'company' => 'Sample Corp',
                'created_at' => now()->toDateTimeString()
            ]
        ];

        // Log the response data
        Log::info('SSO Contacts API response', [
            'contacts_count' => count($contacts),
            'user_id' => $userId,
            'timestamp' => now()
        ]);

        return $this->success([
            'contacts' => $contacts,
            'total' => count($contacts),
            'timestamp' => now()->toDateTimeString(),
            'api_version' => '1.0'
        ], 'SSO Contacts retrieved successfully.');
    }

    public function generateSsoToken(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $payload = [
            'iss' => config('app.url'), // Issuer
            'aud' => 'module-integration', // Audience
            'iat' => time(), // Issued at
            'exp' => time() + (60 * 60 * 24 * 365 * 10), // Expires in 10 years (lifetime)
            'user_id' => $user->id,
            'email' => $user->email,
            'name' => $user->name,
            'type' => $user->type,
        ];

        $token = JWT::encode($payload, env('SSO_SECRET', 'default-secret'), 'HS256');

        Log::info('SSO Token generated for external API call', [
            'user_id' => $user->id,
            'email' => $user->email,
            'timestamp' => now()
        ]);

        return response()->json([
            'success' => true,
            'token' => $token,
            'expires_in' => 60 * 60 * 24 * 365 * 10 // 10 years (lifetime)
        ]);
    }

    /**
     * Validate SSO Token for API requests
     */
    public function validateSsoToken(Request $request)
    {
        $token = $request->bearerToken() ?? $request->get('token');

        if (!$token) {
            return response()->json(['error' => 'SSO token required'], 401);
        }

        try {
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));

            $user = User::find($decoded->user_id);
            if (!$user) {
                return response()->json(['error' => 'Invalid user'], 401);
            }

            // Set the authenticated user for the request
            Auth::setUser($user);

            return $user;
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid or expired SSO token'], 401);
        }
    }

    // Public API endpoints for testing
    public function getApiStatus()
    {
        return $this->success([
            'status' => 'API is working',
            'timestamp' => now()->toDateTimeString(),
            'version' => '1.0',
            'message' => 'Welcome to OMX API'
        ]);
    }

    public function getPublicProjects()
    {
        try {
            $projects = Project::with(['users:id,name,email'])
                ->select('id', 'project_name', 'description', 'status', 'start_date', 'end_date', 'created_at')
                ->take(10)
                ->get()
                ->map(function ($project) {
                    return [
                        'id' => $project->id,
                        'name' => $project->project_name,
                        'description' => $project->description,
                        'status' => $project->status,
                        'status_text' => Project::$project_status[$project->status] ?? 'Unknown',
                        'start_date' => $project->start_date,
                        'end_date' => $project->end_date,
                        'members_count' => $project->users->count(),
                        'members' => $project->users->map(function ($user) {
                            return [
                                'id' => $user->id,
                                'name' => $user->name,
                                'email' => $user->email
                            ];
                        }),
                        'created_at' => $project->created_at
                    ];
                });

            return $this->success([
                'projects' => $projects,
                'total' => $projects->count(),
                'message' => 'Projects retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve projects: ' . $e->getMessage(), 500);
        }
    }

    public function getPublicUsers()
    {
        try {
            $users = User::select('id', 'name', 'email', 'type', 'created_at')
                ->where('type', '!=', 'super admin')
                ->take(10)
                ->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'type' => $user->type,
                        'created_at' => $user->created_at
                    ];
                });

            return $this->success([
                'users' => $users,
                'total' => $users->count(),
                'message' => 'Users retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve users: ' . $e->getMessage(), 500);
        }
    }

    public function getPublicPipelines()
    {
        try {
            // Get the current authenticated user
            $user = auth()->user();

            if (!$user) {
                return $this->error('Unauthorized', 401);
            }

            // Get pipelines created by the current user's company (filtered by user)
            $pipelines = Pipeline::where('created_by', $user->creatorId())
                ->with(['stages' => function($query) use ($user) {
                    $query->where('created_by', $user->ownerId())
                          ->orderBy('order');
                }])
                ->with(['leadStages' => function($query) use ($user) {
                    $query->where('created_by', $user->ownerId())
                          ->orderBy('order');
                }])
                ->select('id', 'name', 'created_by', 'created_at', 'updated_at')
                ->take(10)
                ->get()
                ->map(function ($pipeline) {
                    return [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'order' => $stage->order,
                                'created_at' => $stage->created_at
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'order' => $leadStage->order,
                                'created_at' => $leadStage->created_at
                            ];
                        })
                    ];
                });

            return $this->success([
                'pipelines' => $pipelines,
                'total' => $pipelines->count(),
                'user_info' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'type' => $user->type,
                    'creator_id' => $user->creatorId(),
                    'owner_id' => $user->ownerId()
                ],
                'timestamp' => now()->toDateTimeString(),
                'api_version' => '1.0'
            ], 'Pipelines retrieved successfully for user: ' . $user->name);
        } catch (\Exception $e) {
            \Log::error('Error retrieving pipelines for user', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('Failed to retrieve pipelines: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get all pipelines data for the authenticated user
     * Automatically filters pipelines based on current user's company
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPipelines(Request $request)
    {
        try {
            // Get the current authenticated user
            $user = auth()->user();

            if (!$user) {
                return $this->error('Unauthorized', 401);
            }

            // Get pipelines created by the user's creator (company) - this filters by user's company
            $pipelines = Pipeline::where('created_by', $user->creatorId())
                ->with(['stages' => function($query) use ($user) {
                    $query->where('created_by', $user->ownerId())
                          ->orderBy('order');
                }])
                ->with(['leadStages' => function($query) use ($user) {
                    $query->where('created_by', $user->ownerId())
                          ->orderBy('order');
                }])
                ->get()
                ->map(function ($pipeline) {
                    return [
                        'id' => $pipeline->id,
                        'name' => $pipeline->name,
                        'created_by' => $pipeline->created_by,
                        'created_at' => $pipeline->created_at,
                        'updated_at' => $pipeline->updated_at,
                        'stages' => $pipeline->stages->map(function ($stage) {
                            return [
                                'id' => $stage->id,
                                'name' => $stage->name,
                                'pipeline_id' => $stage->pipeline_id,
                                'order' => $stage->order,
                                'created_by' => $stage->created_by,
                                'created_at' => $stage->created_at,
                                'updated_at' => $stage->updated_at,
                            ];
                        }),
                        'lead_stages' => $pipeline->leadStages->map(function ($leadStage) {
                            return [
                                'id' => $leadStage->id,
                                'name' => $leadStage->name,
                                'pipeline_id' => $leadStage->pipeline_id,
                                'order' => $leadStage->order,
                                'created_by' => $leadStage->created_by,
                                'created_at' => $leadStage->created_at,
                                'updated_at' => $leadStage->updated_at,
                            ];
                        }),
                        'stages_count' => $pipeline->stages->count(),
                        'lead_stages_count' => $pipeline->leadStages->count(),
                    ];
                });

            // Log the API call for debugging
            Log::info('Pipelines API called - User filtered', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'creator_id' => $user->creatorId(),
                'owner_id' => $user->ownerId(),
                'pipelines_count' => $pipelines->count(),
                'timestamp' => now()
            ]);

            return $this->success([
                'pipelines' => $pipelines,
                'total' => $pipelines->count(),
                'user_info' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'type' => $user->type,
                    'creator_id' => $user->creatorId(),
                    'owner_id' => $user->ownerId()
                ],
                'timestamp' => now()->toDateTimeString(),
                'api_version' => '1.0'
            ], 'Pipelines retrieved successfully for user: ' . $user->name);

        } catch (\Exception $e) {
            Log::error('Error retrieving pipelines for user', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('Failed to retrieve pipelines: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get contact list with JWT token authentication
     */
    public function getContactList(Request $request)
    {
        // Get JWT token from request
        $token = $request->bearerToken() ?? $request->get('token') ?? $request->header('X-JWT-Token');

        if (!$token) {
            return response()->json([
                'error' => 'JWT token required',
                'message' => 'Please provide a valid JWT token in Authorization header, token parameter, or X-JWT-Token header'
            ], 401);
        }

        try {
            // Decode JWT token using the same secret as in the payload you provided
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));

            // Check if token is expired
            if ($decoded->exp < time()) {
                return response()->json([
                    'error' => 'Token expired',
                    'message' => 'JWT token has expired. Please generate a new token.'
                ], 401);
            }

            // Find user by email from JWT payload
            $user = User::where('email', $decoded->email)->first();
            if (!$user) {
                return response()->json([
                    'error' => 'Invalid user',
                    'message' => 'User associated with JWT token not found'
                ], 401);
            }

            // Log the API call
            Log::info('Contact List API called', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now(),
                'ip_address' => $request->ip()
            ]);

            // Get contacts from leads and deals for this user
            $contacts = [];

            // Fetch leads data
            $leads = Lead::where('created_by', $user->creatorId())
                ->where('is_active', 1)
                ->select('id', 'name', 'email', 'phone')
                ->get();

            foreach ($leads as $lead) {
                $contacts[] = [
                    'id' => $lead->id,
                    'name' => $lead->name ?? 'No Name',
                    'email' => $lead->email ?? '',
                    'phone' => $lead->phone ?? '',
                    'type' => 'Lead'
                ];
            }

            // Fetch deals data (deals have phone field but no email field)
            $deals = Deal::where('created_by', $user->creatorId())
                ->where('is_active', 1)
                ->select('id', 'name', 'phone')
                ->get();

            foreach ($deals as $deal) {
                $contacts[] = [
                    'id' => $deal->id,
                    'name' => $deal->name ?? 'No Name',
                    'email' => '', // Deals don't have email field
                    'phone' => $deal->phone ?? '',
                    'type' => 'Deal'
                ];
            }

            // Log the response data
            Log::info('Contact List API response', [
                'contacts_count' => count($contacts),
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'contacts' => $contacts,
                    'total' => count($contacts),
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email
                    ]
                ],
                'message' => 'Contact list retrieved successfully',
                'timestamp' => now()->toDateTimeString(),
                'api_version' => '1.0'
            ], 200);

        } catch (\Firebase\JWT\ExpiredException $e) {
            return response()->json([
                'error' => 'Token expired',
                'message' => 'JWT token has expired. Please generate a new token.'
            ], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            return response()->json([
                'error' => 'Invalid token signature',
                'message' => 'JWT token signature is invalid'
            ], 401);
        } catch (\Firebase\JWT\BeforeValidException $e) {
            return response()->json([
                'error' => 'Token not yet valid',
                'message' => 'JWT token is not yet valid'
            ], 401);
        } catch (\Exception $e) {
            Log::error('Contact List API validation failed', [
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_trace' => $e->getTraceAsString(),
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'timestamp' => now()
            ]);

            return response()->json([
                'error' => 'Invalid or malformed JWT token',
                'message' => 'Please provide a valid JWT token'
            ], 401);
        }
    }

}
