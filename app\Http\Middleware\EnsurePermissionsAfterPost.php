<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class EnsurePermissionsAfterPost
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only process for authenticated users and POST requests
        if (Auth::check() && $request->isMethod('post')) {
            $user = Auth::user();
            
            try {
                // Clear permission cache to ensure fresh permissions
                app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

                // Refresh user permissions based on user type
                if ($user->type === 'employee' && !empty($user->module_permissions)) {
                    // For employees, refresh permissions based on their module_permissions
                    $user->refreshPermissions();
                } elseif ($user->pricingPlan && $user->pricingPlan->module_permissions) {
                    // For company users, refresh permissions based on pricing plan
                    $user->refreshPermissions();
                }

                // Log for debugging
                Log::info('Permissions refreshed after POST request', [
                    'user_id' => $user->id,
                    'user_type' => $user->type,
                    'request_url' => $request->url(),
                    'has_pricing_plan' => !is_null($user->pricingPlan),
                    'has_module_permissions' => !empty($user->module_permissions)
                ]);

            } catch (\Exception $e) {
                // Log error but don't fail the request
                Log::error('Failed to refresh permissions after POST request', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'request_url' => $request->url()
                ]);
            }
        }

        return $response;
    }
}
