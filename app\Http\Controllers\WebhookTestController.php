<?php

namespace App\Http\Controllers;

use App\Services\CrmWebhookDispatcher;
use App\Services\ModuleWebhookService;
use App\Models\ModuleIntegration;
use App\Models\Lead;
use App\Models\Deal;
use App\Constants\CrmWebhookActions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookTestController extends Controller
{
    /**
     * Display webhook test page
     */
    public function index()
    {
        $integrations = ModuleIntegration::all();
        $actions = CrmWebhookActions::getAllActions();
        
        return view('webhook-test.index', compact('integrations', 'actions'));
    }
    
    /**
     * Test webhook connectivity for a specific module
     */
    public function testModule(Request $request)
    {
        $request->validate([
            'integration_id' => 'required|exists:module_integrations,id'
        ]);
        
        $integration = ModuleIntegration::findOrFail($request->integration_id);
        $webhookService = new ModuleWebhookService();
        
        $result = $webhookService->testWebhook($integration);
        
        return response()->json([
            'success' => $result['success'] ?? false,
            'message' => $result['success'] ? 'Webhook test successful!' : 'Webhook test failed!',
            'details' => $result
        ]);
    }
    
    /**
     * Send a test webhook with sample data
     */
    public function sendTestWebhook(Request $request)
    {
        $request->validate([
            'action' => 'required|string',
            'integration_id' => 'nullable|exists:module_integrations,id'
        ]);
        
        $dispatcher = new CrmWebhookDispatcher();
        
        // Create sample data based on action type
        $sampleData = $this->generateSampleData($request->action);
        
        if ($request->integration_id) {
            // Send to specific module
            $integration = ModuleIntegration::findOrFail($request->integration_id);
            $webhookService = new ModuleWebhookService();
            $result = $webhookService->sendToModule($integration, $request->action, $sampleData);
            
            return response()->json([
                'success' => $result['success'] ?? false,
                'message' => $result['success'] ? 'Test webhook sent successfully!' : 'Test webhook failed!',
                'details' => $result
            ]);
        } else {
            // Send to all modules
            $results = $dispatcher->dispatch($request->action, $sampleData);
            
            $successCount = 0;
            $totalCount = count($results);
            
            foreach ($results as $result) {
                if (isset($result['success']) && $result['success']) {
                    $successCount++;
                }
            }
            
            return response()->json([
                'success' => $successCount > 0,
                'message' => "Test webhook sent to {$totalCount} modules. {$successCount} successful.",
                'details' => $results
            ]);
        }
    }
    
    /**
     * View webhook logs
     */
    public function viewLogs()
    {
        $logFile = storage_path('logs/webhook.log');
        $logs = [];
        
        if (file_exists($logFile)) {
            $logs = array_reverse(file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES));
            $logs = array_slice($logs, 0, 100); // Show last 100 lines
        }
        
        return view('webhook-test.logs', compact('logs'));
    }
    
    /**
     * Clear webhook logs
     */
    public function clearLogs()
    {
        $logFile = storage_path('logs/webhook.log');
        
        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Webhook logs cleared successfully!'
        ]);
    }
    
    /**
     * Generate sample data for testing
     */
    private function generateSampleData($action)
    {
        switch ($action) {
            case CrmWebhookActions::LEAD_CREATED:
                return [
                    'id' => 999,
                    'name' => 'Test Lead',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890',
                    'subject' => 'Test Lead Subject',
                    'pipeline_id' => 1,
                    'stage_id' => 1,
                    'created_at' => now()->toISOString(),
                    'stage' => [
                        'id' => 1,
                        'name' => 'New'
                    ],
                    'pipeline' => [
                        'id' => 1,
                        'name' => 'Sales Pipeline'
                    ]
                ];
                
            case CrmWebhookActions::DEAL_CREATED:
                return [
                    'id' => 999,
                    'name' => 'Test Deal',
                    'price' => 5000.00,
                    'pipeline_id' => 1,
                    'stage_id' => 1,
                    'status' => 'Active',
                    'created_at' => now()->toISOString(),
                    'stage' => [
                        'id' => 1,
                        'name' => 'Proposal'
                    ],
                    'pipeline' => [
                        'id' => 1,
                        'name' => 'Sales Pipeline'
                    ]
                ];
                
            case CrmWebhookActions::LEAD_CONVERTED_TO_DEAL:
                return [
                    'id' => 999,
                    'name' => 'Test Lead',
                    'email' => '<EMAIL>',
                    'converted_at' => now()->toISOString(),
                    'deal' => [
                        'id' => 888,
                        'name' => 'Converted Deal',
                        'price' => 3000.00
                    ]
                ];
                
            case CrmWebhookActions::LEAD_TASK_CREATED:
                return [
                    'id' => 999,
                    'lead_id' => 888,
                    'name' => 'Test Task',
                    'date' => now()->toDateString(),
                    'priority' => 2,
                    'status' => 0,
                    'created_at' => now()->toISOString()
                ];
                
            case CrmWebhookActions::LEAD_TASK_COMPLETED:
                return [
                    'id' => 999,
                    'lead_id' => 888,
                    'name' => 'Completed Task',
                    'status' => 1,
                    'completed_at' => now()->toISOString()
                ];

            // Booking & Appointment Actions
            case CrmWebhookActions::APPOINTMENT_SCHEDULED:
                return [
                    'id' => 999,
                    'title' => 'Test Consultation',
                    'start_time' => now()->addDays(1)->toISOString(),
                    'end_time' => now()->addDays(1)->addHour()->toISOString(),
                    'location' => 'Conference Room A',
                    'client_name' => 'John Doe',
                    'client_email' => '<EMAIL>',
                    'status' => 'confirmed',
                    'service' => [
                        'id' => 5,
                        'name' => 'Business Consultation'
                    ]
                ];

            case CrmWebhookActions::APPOINTMENT_RESCHEDULED:
                return [
                    'id' => 999,
                    'title' => 'Rescheduled Meeting',
                    'old_date_time' => now()->addDays(1)->toISOString(),
                    'new_date_time' => now()->addDays(2)->toISOString(),
                    'client_name' => 'Jane Smith',
                    'reason' => 'Client requested change'
                ];

            case CrmWebhookActions::APPOINTMENT_CANCELLED:
                return [
                    'id' => 999,
                    'title' => 'Cancelled Appointment',
                    'scheduled_time' => now()->addDays(1)->toISOString(),
                    'client_name' => 'Bob Johnson',
                    'cancellation_reason' => 'Customer requested cancellation',
                    'cancelled_at' => now()->toISOString()
                ];

            case CrmWebhookActions::BOOKING_FORM_SUBMITTED:
                return [
                    'id' => 999,
                    'form_name' => 'Consultation Booking',
                    'client_name' => 'Alice Brown',
                    'client_email' => '<EMAIL>',
                    'requested_date' => now()->addDays(3)->toDateString(),
                    'requested_time' => '14:00',
                    'service_type' => 'Business Consultation',
                    'form_data' => [
                        'phone' => '+1234567890',
                        'company' => 'ABC Corp',
                        'message' => 'Looking for business consultation'
                    ]
                ];

            case CrmWebhookActions::EVENT_CREATED:
                return [
                    'id' => 999,
                    'title' => 'Team Meeting',
                    'description' => 'Weekly team sync',
                    'start_time' => now()->addDays(1)->toISOString(),
                    'end_time' => now()->addDays(1)->addHours(2)->toISOString(),
                    'location' => 'Main Conference Room',
                    'attendees' => ['<EMAIL>', '<EMAIL>']
                ];

            default:
                return [
                    'id' => 999,
                    'test_data' => true,
                    'action' => $action,
                    'timestamp' => now()->toISOString(),
                    'message' => 'This is test data for action: ' . $action
                ];
        }
    }
}
