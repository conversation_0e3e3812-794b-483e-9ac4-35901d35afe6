<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Deal;
use App\Services\CrmWebhookDispatcher;
use Carbon\Carbon;

class CheckDealWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deals:check-webhooks 
                            {--deadline-days=3 : Days before deadline to trigger webhook}
                            {--inactive-days=7 : Days of inactivity to trigger webhook}
                            {--dry-run : Run without sending webhooks}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for deals with approaching deadlines or inactive for specified days and trigger webhooks';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $deadlineDays = (int) $this->option('deadline-days');
        $inactiveDays = (int) $this->option('inactive-days');
        $dryRun = $this->option('dry-run');

        $this->info("Checking deals for webhook triggers...");
        $this->info("Deadline warning: {$deadlineDays} days");
        $this->info("Inactive threshold: {$inactiveDays} days");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No webhooks will be sent");
        }

        $webhookDispatcher = new CrmWebhookDispatcher();
        $deadlineCount = 0;
        $inactiveCount = 0;

        // Check for deals with approaching deadlines
        $this->info("\n--- Checking for approaching deadlines ---");
        
        $dealsWithApproachingDeadlines = Deal::whereNotNull('deadline')
            ->where('deadline', '<=', Carbon::now()->addDays($deadlineDays))
            ->where('deadline', '>', Carbon::now())
            ->where('status', 'Active')
            ->get();

        foreach ($dealsWithApproachingDeadlines as $deal) {
            $daysUntilDeadline = Carbon::now()->diffInDays(Carbon::parse($deal->deadline));
            
            $this->line("Deal #{$deal->id} '{$deal->name}' - Deadline in {$daysUntilDeadline} days ({$deal->deadline})");
            
            if (!$dryRun) {
                try {
                    $webhookDispatcher->dispatchDealDeadlineApproaching($deal, $daysUntilDeadline);
                    $this->info("  ✓ Webhook sent successfully");
                    $deadlineCount++;
                } catch (\Exception $e) {
                    $this->error("  ✗ Webhook failed: " . $e->getMessage());
                }
            } else {
                $this->comment("  → Would send deadline webhook");
                $deadlineCount++;
            }
        }

        // Check for inactive deals
        $this->info("\n--- Checking for inactive deals ---");
        
        $inactiveDeals = Deal::where('updated_at', '<=', Carbon::now()->subDays($inactiveDays))
            ->where('status', 'Active')
            ->get();

        foreach ($inactiveDeals as $deal) {
            $daysSinceUpdate = Carbon::now()->diffInDays(Carbon::parse($deal->updated_at));
            
            $this->line("Deal #{$deal->id} '{$deal->name}' - Inactive for {$daysSinceUpdate} days (last update: {$deal->updated_at})");
            
            if (!$dryRun) {
                try {
                    $webhookDispatcher->dispatchDealInactiveForDays($deal, $daysSinceUpdate);
                    $this->info("  ✓ Webhook sent successfully");
                    $inactiveCount++;
                } catch (\Exception $e) {
                    $this->error("  ✗ Webhook failed: " . $e->getMessage());
                }
            } else {
                $this->comment("  → Would send inactive webhook");
                $inactiveCount++;
            }
        }

        // Summary
        $this->info("\n--- Summary ---");
        $this->info("Deadline webhooks: {$deadlineCount}");
        $this->info("Inactive webhooks: {$inactiveCount}");
        $this->info("Total webhooks: " . ($deadlineCount + $inactiveCount));

        if ($dryRun) {
            $this->warn("This was a dry run - no actual webhooks were sent");
            $this->info("Run without --dry-run to send webhooks");
        }

        return Command::SUCCESS;
    }
}
